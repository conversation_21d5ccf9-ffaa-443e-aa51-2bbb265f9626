{"name": "base-bailey-json", "version": "1.0.0", "description": "", "main": "dist/app.js", "type": "module", "scripts": {"start": "node ./dist/app.js", "lint": "eslint . --no-ignore", "dev": "npm run lint && nodemon ./src/app.ts", "build": "npx rollup -c", "setup": "node ./scripts/setup-env.js", "test:firebase": "node ./scripts/test-firebase.js", "regenerate-zoho-tokens": "node ./scripts/regenerate-zoho-tokens.cjs"}, "keywords": [], "dependencies": {"@builderbot/bot": "^1.2.9", "@builderbot/provider-baileys": "^1.2.9", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "firebase-admin": "^12.5.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.45", "nanoid": "^5.1.2", "qs": "^6.14.0", "zod": "^3.25.74", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.4.0", "eslint": "^8.52.0", "eslint-plugin-builderbot": "latest", "nodemon": "^3.1.0", "rollup": "^4.10.0", "rollup-plugin-typescript2": "^0.36.0", "tsx": "^4.7.1", "typescript": "^5.4.3"}, "author": "", "license": "ISC"}