import { addKeyword, EVENTS } from '@builderbot/bot';
import firebaseServiceInstance from '~/database/firebaseService';
import { cancelAppointment } from '~/services/appointmentService';

export const flowCancelAppointment = addKeyword(EVENTS.ACTION).addAnswer(
  'Cancelando cita....',
  null,
  async (ctx, { flowDynamic, state }) => {
    try {
      const myState = state.getMyState();
      const { userData: usersAppointmentData, updateTokenState } = myState;
      let deleteAppointmentResult = false;

      const appointmentParams = {
        eventUid: usersAppointmentData.eventUid,
        etag: usersAppointmentData.etag,
        phone: ctx.from,
      };

      console.log('🔄 Iniciando cancelación de cita...', appointmentParams);

      try {
        if (updateTokenState) {
          console.log('🔄 Cancelando cita y marcando token...');
          const [result] = await Promise.all([
            cancelAppointment(appointmentParams),
            firebaseServiceInstance.markAppointmentTokenUsed(
              usersAppointmentData.tokenCode
            ),
          ]);

          deleteAppointmentResult = result;
        } else {
          console.log('🔄 Cancelando cita...');
          deleteAppointmentResult = await cancelAppointment(appointmentParams);
        }
      } catch (cancelError) {
        console.error('❌ Error en cancelación:', cancelError);
        // Continuar con el flujo aunque haya error
        deleteAppointmentResult = false;
      }

      console.log('📊 Resultado de cancelación:', { deleteAppointmentResult });

      if (deleteAppointmentResult) {
        await flowDynamic(
          `¡Cita cancelada con exito!\n\nEscribe "*menu*" para volver al menu principal 😄`
        );
      } else {
        await flowDynamic(
          'Error al cancelar la cita 😔 porfavor intente más tarde.\n\nEscribe "*menu*" para volver al menu principal 😄'
        );
      }
    } catch (error) {
      console.error('Error al cancelar la cita:', error);
      await flowDynamic(
        'Error al cancelar la cita, porfavor intente más tarde.\n\nEscribe "*menu*" para volver al menu principal 😄'
      );
    } finally {
      await state.update({
        updateTokenState: false,
      });
    }
  }
);
