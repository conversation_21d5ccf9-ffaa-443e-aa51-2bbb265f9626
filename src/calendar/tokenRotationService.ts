import { tokenManager } from './tokenManager';
import { tokenBackupService } from './tokenBackupService';
import { secureTokenStorage } from './secureTokenStorage';

/**
 * Interface for rotation audit log entry
 */
interface RotationAuditEntry {
  rotationId: string;
  timestamp: number;
  type: 'automatic' | 'manual';
  trigger: 'scheduled' | 'expiration' | 'manual_request' | 'security_update';
  status: 'initiated' | 'completed' | 'failed' | 'rolled_back';
  oldKeyInfo?: {
    algorithm: string;
    timestamp: number;
  };
  newKeyInfo?: {
    algorithm: string;
    timestamp: number;
  };
  backupId?: string;
  error?: string;
}

/**
 * Interface for rotation configuration
 */
interface RotationConfig {
  automaticRotationEnabled: boolean;
  rotationIntervalDays: number; // How often to rotate encryption keys
  maxRotationRetries: number;
  rollbackTimeoutMinutes: number;
}

/**
 * Token Rotation Service
 * Provides encryption key rotation and audit trail functionality
 */
export class TokenRotationService {
  private config: RotationConfig;
  private auditLog: RotationAuditEntry[] = [];
  private rotationSchedule: NodeJS.Timeout | null = null;

  constructor(config?: Partial<RotationConfig>) {
    this.config = {
      automaticRotationEnabled: false, // Disabled by default for safety
      rotationIntervalDays: 90, // Rotate every 90 days
      maxRotationRetries: 3,
      rollbackTimeoutMinutes: 30,
      ...config,
    };

    this.loadAuditLog();
  }

  /**
   * Load audit log from storage (placeholder - could be file or database)
   */
  private loadAuditLog(): void {
    // In a real implementation, this would load from persistent storage
    // For now, we'll keep it in memory
    this.auditLog = [];
  }

  /**
   * Save audit log entry
   */
  private saveAuditEntry(entry: RotationAuditEntry): void {
    this.auditLog.push(entry);

    // Keep only last 100 entries
    if (this.auditLog.length > 100) {
      this.auditLog = this.auditLog.slice(-100);
    }

    console.log(
      `📝 Rotation audit entry saved: ${entry.rotationId} - ${entry.status}`
    );
  }

  /**
   * Generate new encryption key
   */
  private generateNewEncryptionKey(): string {
    // Generate random bytes for encryption key
    const array = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // Fallback for Node.js environment
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return Buffer.from(array).toString('base64');
  }

  /**
   * Validate that new key works correctly
   */
  private async validateNewKey(newKey: string): Promise<boolean> {
    try {
      // Test encryption/decryption with new key
      const testData = {
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresIn: Date.now() + 3600000,
      };

      // Temporarily set new key
      const originalKey = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = newKey;

      // Test encryption/decryption
      const encrypted = secureTokenStorage.encryptTokens(testData);
      const decrypted = secureTokenStorage.decryptTokens(encrypted);

      // Restore original key
      if (originalKey) {
        process.env.ZOHO_TOKEN_ENCRYPTION_KEY = originalKey;
      }

      // Validate decryption worked
      return (
        decrypted.accessToken === testData.accessToken &&
        decrypted.refreshToken === testData.refreshToken &&
        decrypted.expiresIn === testData.expiresIn
      );
    } catch (error) {
      console.error('New key validation failed:', error);
      return false;
    }
  }

  /**
   * Perform encryption key rotation
   */
  public async rotateEncryptionKey(
    type: 'automatic' | 'manual' = 'manual',
    trigger:
      | 'scheduled'
      | 'expiration'
      | 'manual_request'
      | 'security_update' = 'manual_request'
  ): Promise<{ success: boolean; rotationId: string; error?: string }> {
    const rotationId = `rotation_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    console.log(`🔄 Starting encryption key rotation: ${rotationId}`);
    console.log(`   Type: ${type}, Trigger: ${trigger}`);

    // Create initial audit entry
    const auditEntry: RotationAuditEntry = {
      rotationId,
      timestamp: Date.now(),
      type,
      trigger,
      status: 'initiated',
    };

    this.saveAuditEntry(auditEntry);

    try {
      // Step 1: Read current tokens
      const currentTokens = tokenManager.readTokens();
      if (!currentTokens) {
        throw new Error('No tokens found to rotate');
      }

      // Step 2: Create backup with current encryption
      console.log('📦 Creating pre-rotation backup...');
      const backupId = await tokenBackupService.createBackup(
        currentTokens,
        'manual',
        'manual_backup'
      );

      if (!backupId) {
        throw new Error('Failed to create pre-rotation backup');
      }

      auditEntry.backupId = backupId;

      // Step 3: Generate new encryption key
      console.log('🔑 Generating new encryption key...');
      const newKey = this.generateNewEncryptionKey();

      // Step 4: Validate new key
      console.log('✅ Validating new encryption key...');
      const isValidKey = await this.validateNewKey(newKey);
      if (!isValidKey) {
        throw new Error('New encryption key validation failed');
      }

      // Step 5: Store old key info
      auditEntry.oldKeyInfo = {
        algorithm: 'aes-256-gcm',
        timestamp: Date.now(),
      };

      // Step 6: Update environment with new key
      console.log('🔄 Updating encryption key...');
      const oldKey = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = newKey;

      // Step 7: Re-encrypt tokens with new key
      console.log('🔐 Re-encrypting tokens with new key...');
      tokenManager.saveTokens(
        currentTokens.accessToken,
        currentTokens.refreshToken,
        currentTokens.expiresIn
      );

      // Step 8: Verify re-encrypted tokens can be read
      console.log('✅ Verifying re-encrypted tokens...');
      const verifyTokens = tokenManager.readTokens();
      if (
        !verifyTokens ||
        verifyTokens.accessToken !== currentTokens.accessToken ||
        verifyTokens.refreshToken !== currentTokens.refreshToken
      ) {
        throw new Error('Token verification failed after rotation');
      }

      // Step 9: Update audit entry with success
      auditEntry.status = 'completed';
      auditEntry.newKeyInfo = {
        algorithm: 'aes-256-gcm',
        timestamp: Date.now(),
      };

      this.saveAuditEntry(auditEntry);

      console.log(
        `✅ Encryption key rotation completed successfully: ${rotationId}`
      );
      console.log(`📦 Backup created: ${backupId}`);
      console.log(
        '⚠️  IMPORTANT: Update your .env file with the new encryption key!'
      );
      console.log(`   New key: ${newKey}`);

      return { success: true, rotationId };
    } catch (error) {
      console.error(`❌ Encryption key rotation failed: ${error}`);

      // Update audit entry with failure
      auditEntry.status = 'failed';
      auditEntry.error =
        error instanceof Error ? error.message : 'Unknown error';
      this.saveAuditEntry(auditEntry);

      return {
        success: false,
        rotationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Rollback to previous encryption key (emergency function)
   */
  public async rollbackRotation(rotationId: string): Promise<boolean> {
    try {
      console.log(`🔄 Rolling back rotation: ${rotationId}`);

      // Find the rotation in audit log
      const rotationEntry = this.auditLog.find(
        (entry) => entry.rotationId === rotationId
      );
      if (!rotationEntry) {
        throw new Error(`Rotation ${rotationId} not found in audit log`);
      }

      if (rotationEntry.status !== 'completed') {
        throw new Error(
          `Cannot rollback rotation with status: ${rotationEntry.status}`
        );
      }

      if (!rotationEntry.backupId) {
        throw new Error('No backup ID found for rollback');
      }

      // Restore from backup
      console.log(`📦 Restoring from backup: ${rotationEntry.backupId}`);
      const restoredTokens = await tokenBackupService.restoreFromBackup(
        rotationEntry.backupId
      );

      if (!restoredTokens) {
        throw new Error('Failed to restore tokens from backup');
      }

      // Update audit entry
      rotationEntry.status = 'rolled_back';
      this.saveAuditEntry(rotationEntry);

      console.log(`✅ Rotation rollback completed: ${rotationId}`);
      return true;
    } catch (error) {
      console.error(`❌ Rollback failed: ${error}`);
      return false;
    }
  }

  /**
   * Start automatic rotation schedule
   */
  public startAutomaticRotation(): void {
    if (!this.config.automaticRotationEnabled) {
      console.log('⚠️  Automatic rotation is disabled in configuration');
      return;
    }

    if (this.rotationSchedule) {
      console.log('⚠️  Automatic rotation is already scheduled');
      return;
    }

    const intervalMs = this.config.rotationIntervalDays * 24 * 60 * 60 * 1000;

    console.log(
      `🕐 Scheduling automatic key rotation every ${this.config.rotationIntervalDays} days`
    );

    this.rotationSchedule = setInterval(async () => {
      console.log('🔄 Automatic key rotation triggered');
      const result = await this.rotateEncryptionKey('automatic', 'scheduled');

      if (!result.success) {
        console.error(`❌ Automatic rotation failed: ${result.error}`);
      }
    }, intervalMs);

    console.log('✅ Automatic rotation schedule started');
  }

  /**
   * Stop automatic rotation schedule
   */
  public stopAutomaticRotation(): void {
    if (this.rotationSchedule) {
      clearInterval(this.rotationSchedule);
      this.rotationSchedule = null;
      console.log('🛑 Automatic rotation schedule stopped');
    }
  }

  /**
   * Get rotation audit log
   */
  public getAuditLog(): RotationAuditEntry[] {
    return [...this.auditLog]; // Return copy to prevent modification
  }

  /**
   * Get rotation statistics
   */
  public getRotationStats(): object {
    const total = this.auditLog.length;
    const completed = this.auditLog.filter(
      (entry) => entry.status === 'completed'
    ).length;
    const failed = this.auditLog.filter(
      (entry) => entry.status === 'failed'
    ).length;
    const rolledBack = this.auditLog.filter(
      (entry) => entry.status === 'rolled_back'
    ).length;

    const lastRotation = this.auditLog
      .filter((entry) => entry.status === 'completed')
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    return {
      total,
      completed,
      failed,
      rolledBack,
      successRate:
        total > 0 ? ((completed / total) * 100).toFixed(2) + '%' : 'N/A',
      lastRotation: lastRotation
        ? {
            rotationId: lastRotation.rotationId,
            timestamp: new Date(lastRotation.timestamp).toISOString(),
            type: lastRotation.type,
            trigger: lastRotation.trigger,
          }
        : null,
      nextScheduledRotation: this.rotationSchedule
        ? `In ${this.config.rotationIntervalDays} days`
        : 'Not scheduled',
    };
  }

  /**
   * Get rotation service status
   */
  public getRotationStatus(): object {
    return {
      config: this.config,
      automaticRotationActive: this.rotationSchedule !== null,
      auditLogEntries: this.auditLog.length,
      stats: this.getRotationStats(),
    };
  }

  /**
   * Update rotation configuration
   */
  public updateConfig(newConfig: Partial<RotationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️  Token rotation configuration updated:', this.config);

    // Restart automatic rotation if configuration changed
    if (this.rotationSchedule) {
      this.stopAutomaticRotation();
      if (this.config.automaticRotationEnabled) {
        this.startAutomaticRotation();
      }
    }
  }
}

/**
 * Singleton instance for token rotation service
 */
export const tokenRotationService = new TokenRotationService();
