import { tokenManager } from './tokenManager';
import { tokenBackupService } from './tokenBackupService';

/**
 * Interface for token health status
 */
interface TokenHealthStatus {
  isValid: boolean;
  expiresAt: number;
  expiresIn: number; // milliseconds until expiration
  isExpired: boolean;
  isNearExpiration: boolean;
  warningThreshold: number;
  criticalThreshold: number;
  lastChecked: number;
}

/**
 * Interface for monitoring configuration
 */
interface MonitoringConfig {
  warningThresholdMinutes: number; // Warn when token expires within this time
  criticalThresholdMinutes: number; // Critical alert when token expires within this time
  checkIntervalMinutes: number; // How often to check token status
  autoRefreshEnabled: boolean; // Whether to automatically trigger refresh
}

/**
 * Token Expiration Monitoring Service
 * Provides proactive monitoring and alerting for Zoho token expiration
 */
export class TokenMonitor {
  private config: MonitoringConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private lastHealthCheck: TokenHealthStatus | null = null;

  constructor(config?: Partial<MonitoringConfig>) {
    this.config = {
      warningThresholdMinutes: 60, // 1 hour warning
      criticalThresholdMinutes: 15, // 15 minutes critical
      checkIntervalMinutes: 30, // Check every 30 minutes
      autoRefreshEnabled: true,
      ...config,
    };
  }

  /**
   * Start continuous token monitoring
   */
  public startMonitoring(): void {
    if (this.monitoringInterval) {
      console.log('⚠️  Token monitoring is already running');
      return;
    }

    console.log('🔍 Starting token expiration monitoring...');
    console.log(`📊 Monitoring config:`, {
      warningThreshold: `${this.config.warningThresholdMinutes} minutes`,
      criticalThreshold: `${this.config.criticalThresholdMinutes} minutes`,
      checkInterval: `${this.config.checkIntervalMinutes} minutes`,
      autoRefresh: this.config.autoRefreshEnabled,
    });

    // Perform initial check
    this.performHealthCheck();

    // Set up periodic monitoring
    const intervalMs = this.config.checkIntervalMinutes * 60 * 1000;
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck();
    }, intervalMs);

    console.log('✅ Token monitoring started successfully');
  }

  /**
   * Stop token monitoring
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('🛑 Token monitoring stopped');
    }
  }

  /**
   * Perform a health check on current tokens
   */
  public performHealthCheck(): TokenHealthStatus {
    try {
      console.log('🔍 Performing token health check...');

      const tokens = tokenManager.readTokens();
      const now = Date.now();

      if (!tokens) {
        const status: TokenHealthStatus = {
          isValid: false,
          expiresAt: 0,
          expiresIn: 0,
          isExpired: true,
          isNearExpiration: false,
          warningThreshold: this.config.warningThresholdMinutes * 60 * 1000,
          criticalThreshold: this.config.criticalThresholdMinutes * 60 * 1000,
          lastChecked: now,
        };

        this.lastHealthCheck = status;
        this.handleTokenAlert('NO_TOKENS', status);
        return status;
      }

      const expiresAt = tokens.expiresIn;
      const expiresIn = expiresAt - now;
      const isExpired = expiresIn <= 0;
      const warningThresholdMs = this.config.warningThresholdMinutes * 60 * 1000;
      const criticalThresholdMs = this.config.criticalThresholdMinutes * 60 * 1000;
      const isNearExpiration = expiresIn <= warningThresholdMs;

      const status: TokenHealthStatus = {
        isValid: !isExpired,
        expiresAt,
        expiresIn,
        isExpired,
        isNearExpiration,
        warningThreshold: warningThresholdMs,
        criticalThreshold: criticalThresholdMs,
        lastChecked: now,
      };

      this.lastHealthCheck = status;

      // Handle alerts based on status
      if (isExpired) {
        this.handleTokenAlert('EXPIRED', status);
      } else if (expiresIn <= criticalThresholdMs) {
        this.handleTokenAlert('CRITICAL', status);
      } else if (isNearExpiration) {
        this.handleTokenAlert('WARNING', status);
      } else {
        console.log('✅ Token health check passed - tokens are valid');
      }

      return status;
    } catch (error) {
      console.error('❌ Token health check failed:', error);
      
      const errorStatus: TokenHealthStatus = {
        isValid: false,
        expiresAt: 0,
        expiresIn: 0,
        isExpired: true,
        isNearExpiration: false,
        warningThreshold: this.config.warningThresholdMinutes * 60 * 1000,
        criticalThreshold: this.config.criticalThresholdMinutes * 60 * 1000,
        lastChecked: Date.now(),
      };

      this.lastHealthCheck = errorStatus;
      return errorStatus;
    }
  }

  /**
   * Handle token alerts based on severity
   */
  private handleTokenAlert(
    alertType: 'NO_TOKENS' | 'EXPIRED' | 'CRITICAL' | 'WARNING',
    status: TokenHealthStatus
  ): void {
    const timeUntilExpiration = this.formatDuration(status.expiresIn);

    switch (alertType) {
      case 'NO_TOKENS':
        console.error('🚨 ALERT: No Zoho tokens found!');
        console.error('   Action required: Re-authenticate with Zoho Calendar');
        break;

      case 'EXPIRED':
        console.error('🚨 CRITICAL: Zoho tokens have EXPIRED!');
        console.error('   Action required: Immediate token refresh needed');
        if (this.config.autoRefreshEnabled) {
          this.triggerTokenRefresh();
        }
        break;

      case 'CRITICAL':
        console.warn(`🔥 CRITICAL: Zoho tokens expire in ${timeUntilExpiration}!`);
        console.warn('   Action required: Token refresh needed soon');
        if (this.config.autoRefreshEnabled) {
          this.triggerTokenRefresh();
        }
        break;

      case 'WARNING':
        console.warn(`⚠️  WARNING: Zoho tokens expire in ${timeUntilExpiration}`);
        console.warn('   Recommendation: Consider refreshing tokens soon');
        break;
    }

    // Log detailed status for debugging
    console.log('📊 Token Status Details:', {
      expiresAt: new Date(status.expiresAt).toISOString(),
      expiresIn: timeUntilExpiration,
      isExpired: status.isExpired,
      isNearExpiration: status.isNearExpiration,
    });
  }

  /**
   * Trigger automatic token refresh (placeholder for integration with ClassZohoApi)
   */
  private triggerTokenRefresh(): void {
    console.log('🔄 Triggering automatic token refresh...');
    
    // Note: In a real implementation, this would call the ClassZohoApi refresh method
    // For now, we'll just log the action and create a backup
    console.log('ℹ️  Auto-refresh would be triggered here (integration pending)');
    
    // Create a backup before any refresh attempt
    const tokens = tokenManager.readTokens();
    if (tokens) {
      tokenBackupService.createBackup(tokens, 'automatic', 'token_refresh')
        .then((backupId) => {
          if (backupId) {
            console.log(`📦 Pre-refresh backup created: ${backupId}`);
          }
        })
        .catch((error) => {
          console.error('⚠️  Pre-refresh backup failed:', error);
        });
    }
  }

  /**
   * Format duration in milliseconds to human-readable string
   */
  private formatDuration(ms: number): string {
    if (ms <= 0) return 'expired';

    const minutes = Math.floor(ms / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day(s) ${hours % 24} hour(s)`;
    } else if (hours > 0) {
      return `${hours} hour(s) ${minutes % 60} minute(s)`;
    } else {
      return `${minutes} minute(s)`;
    }
  }

  /**
   * Get current monitoring status
   */
  public getMonitoringStatus(): object {
    return {
      isRunning: this.monitoringInterval !== null,
      config: this.config,
      lastHealthCheck: this.lastHealthCheck,
      nextCheckIn: this.monitoringInterval 
        ? `${this.config.checkIntervalMinutes} minutes`
        : 'Not scheduled',
    };
  }

  /**
   * Get last health check result
   */
  public getLastHealthCheck(): TokenHealthStatus | null {
    return this.lastHealthCheck;
  }

  /**
   * Update monitoring configuration
   */
  public updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️  Token monitoring configuration updated:', this.config);

    // Restart monitoring if it's currently running
    if (this.monitoringInterval) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * Create health check endpoint data
   */
  public getHealthCheckEndpoint(): object {
    const status = this.performHealthCheck();
    
    return {
      status: status.isValid ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      details: {
        tokenValid: status.isValid,
        expiresAt: new Date(status.expiresAt).toISOString(),
        expiresIn: this.formatDuration(status.expiresIn),
        isExpired: status.isExpired,
        isNearExpiration: status.isNearExpiration,
      },
      monitoring: {
        isRunning: this.monitoringInterval !== null,
        checkInterval: `${this.config.checkIntervalMinutes} minutes`,
        autoRefreshEnabled: this.config.autoRefreshEnabled,
      },
    };
  }
}

/**
 * Singleton instance for token monitoring
 */
export const tokenMonitor = new TokenMonitor();
