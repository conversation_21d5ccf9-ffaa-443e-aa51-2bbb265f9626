import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import axios from 'axios';
import { tokenManager } from '../tokenManager';
import { tokenBackupService } from '../tokenBackupService';
import { tokenMonitor } from '../tokenMonitor';

// Mock axios for API calls
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock ClassZohoApi to avoid actual API calls
jest.mock('../ClassZohoApi', () => {
  return {
    __esModule: true,
    default: {
      getAccessTokenZoho: jest.fn(),
      renewAccessToken: jest.fn(),
      getCalendars: jest.fn(),
      getEventsByRange: jest.fn(),
      getInstance: jest.fn().mockReturnThis(),
    },
  };
});

describe('Zoho API Integration with Encrypted Storage', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    process.env.ZOHO_TOKEN_ENCRYPTION_KEY = 'dGVzdF9lbmNyeXB0aW9uX2tleV8zMl9ieXRlc19sb25n';
    process.env.ZOHO_TOKEN_BACKUP_ENABLED = 'true';
    process.env.ZOHO_CLIENT_ID = 'test_client_id';
    process.env.ZOHO_CLIENT_SECRET = 'test_client_secret';
    process.env.ZOHO_REDIRECT_URL = 'http://localhost:3000/oauth2callback';
    process.env.ZOHO_CALENDAR_UID = 'test_calendar_uid';
    
    jest.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Token Storage Integration', () => {
    test('should store tokens securely after API authentication', async () => {
      // Mock successful token response
      const mockTokenResponse = {
        data: {
          access_token: 'new_access_token_123',
          refresh_token: 'new_refresh_token_456',
          expires_in: 3600,
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockTokenResponse);

      // Simulate token saving (this would normally happen in ClassZohoApi)
      const expiresAt = Date.now() + mockTokenResponse.data.expires_in * 1000;
      tokenManager.saveTokens(
        mockTokenResponse.data.access_token,
        mockTokenResponse.data.refresh_token,
        expiresAt
      );

      // Verify tokens are stored and can be retrieved
      const storedTokens = tokenManager.readTokens();
      expect(storedTokens).not.toBeNull();
      expect(storedTokens?.accessToken).toBe(mockTokenResponse.data.access_token);
      expect(storedTokens?.refreshToken).toBe(mockTokenResponse.data.refresh_token);
      expect(storedTokens?.expiresIn).toBe(expiresAt);

      // Verify storage status shows encryption is working
      const status = tokenManager.getStorageStatus();
      expect(status).toHaveProperty('encryptionAvailable', true);
    });

    test('should handle token refresh with encrypted storage', async () => {
      // Set up initial tokens
      const initialTokens = {
        accessToken: 'initial_access_token',
        refreshToken: 'initial_refresh_token',
        expiresIn: Date.now() + 3600000,
      };

      tokenManager.saveTokens(
        initialTokens.accessToken,
        initialTokens.refreshToken,
        initialTokens.expiresIn
      );

      // Mock refresh token response
      const mockRefreshResponse = {
        data: {
          access_token: 'refreshed_access_token',
          expires_in: 3600,
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockRefreshResponse);

      // Simulate token refresh
      const newExpiresAt = Date.now() + mockRefreshResponse.data.expires_in * 1000;
      tokenManager.saveTokens(
        mockRefreshResponse.data.access_token,
        initialTokens.refreshToken, // Keep same refresh token
        newExpiresAt
      );

      // Verify refreshed tokens are stored
      const refreshedTokens = tokenManager.readTokens();
      expect(refreshedTokens?.accessToken).toBe(mockRefreshResponse.data.access_token);
      expect(refreshedTokens?.refreshToken).toBe(initialTokens.refreshToken);
    });

    test('should create backup during token operations', async () => {
      const testTokens = {
        accessToken: 'backup_test_token',
        refreshToken: 'backup_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Mock Firebase backup operations
      const mockBackupId = 'backup_123456789';
      jest.spyOn(tokenBackupService, 'createBackup').mockResolvedValue(mockBackupId);

      // Save tokens (should trigger automatic backup)
      tokenManager.saveTokens(
        testTokens.accessToken,
        testTokens.refreshToken,
        testTokens.expiresIn
      );

      // Wait for async backup operation
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify backup was attempted
      expect(tokenBackupService.createBackup).toHaveBeenCalledWith(
        expect.objectContaining({
          accessToken: testTokens.accessToken,
          refreshToken: testTokens.refreshToken,
        }),
        'automatic',
        'token_update'
      );
    });
  });

  describe('Error Handling and Fallback', () => {
    test('should handle encryption failure gracefully', () => {
      // Set invalid encryption key
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = 'invalid_key';

      // Should fall back to plain text storage
      const testTokens = {
        accessToken: 'fallback_test_token',
        refreshToken: 'fallback_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // This should not throw an error
      expect(() => {
        tokenManager.saveTokens(
          testTokens.accessToken,
          testTokens.refreshToken,
          testTokens.expiresIn
        );
      }).not.toThrow();

      // Storage status should indicate encryption is not available
      const status = tokenManager.getStorageStatus();
      expect(status).toHaveProperty('encryptionAvailable', false);
    });

    test('should handle API errors during token refresh', async () => {
      // Mock API error response
      const mockError = {
        response: {
          data: {
            error: 'invalid_grant',
            error_description: 'The provided authorization grant is invalid',
          },
          status: 400,
        },
      };

      mockedAxios.post.mockRejectedValueOnce(mockError);

      // Simulate error handling (this would normally be in ClassZohoApi)
      try {
        await mockedAxios.post('https://accounts.zoho.com/oauth/v2/token', {});
      } catch (error) {
        expect(error).toBe(mockError);
        
        // Verify error structure
        if (axios.isAxiosError(error)) {
          expect(error.response?.status).toBe(400);
          expect(error.response?.data.error).toBe('invalid_grant');
        }
      }
    });

    test('should handle network connectivity issues', async () => {
      // Mock network error
      const networkError = new Error('Network Error');
      mockedAxios.post.mockRejectedValueOnce(networkError);

      try {
        await mockedAxios.post('https://accounts.zoho.com/oauth/v2/token', {});
      } catch (error) {
        expect(error).toBe(networkError);
        expect((error as Error).message).toBe('Network Error');
      }
    });
  });

  describe('Token Monitoring Integration', () => {
    test('should monitor token expiration correctly', () => {
      // Set up tokens that expire soon
      const soonToExpireTokens = {
        accessToken: 'expiring_token',
        refreshToken: 'expiring_refresh',
        expiresIn: Date.now() + 30 * 60 * 1000, // 30 minutes
      };

      tokenManager.saveTokens(
        soonToExpireTokens.accessToken,
        soonToExpireTokens.refreshToken,
        soonToExpireTokens.expiresIn
      );

      // Perform health check
      const healthCheck = tokenMonitor.performHealthCheck();

      expect(healthCheck.isValid).toBe(true);
      expect(healthCheck.isNearExpiration).toBe(true);
      expect(healthCheck.expiresIn).toBeLessThan(60 * 60 * 1000); // Less than 1 hour
    });

    test('should detect expired tokens', () => {
      // Set up expired tokens
      const expiredTokens = {
        accessToken: 'expired_token',
        refreshToken: 'expired_refresh',
        expiresIn: Date.now() - 3600000, // 1 hour ago
      };

      tokenManager.saveTokens(
        expiredTokens.accessToken,
        expiredTokens.refreshToken,
        expiredTokens.expiresIn
      );

      // Perform health check
      const healthCheck = tokenMonitor.performHealthCheck();

      expect(healthCheck.isValid).toBe(false);
      expect(healthCheck.isExpired).toBe(true);
      expect(healthCheck.expiresIn).toBeLessThan(0);
    });

    test('should handle missing tokens in monitoring', () => {
      // Ensure no tokens are present
      jest.spyOn(tokenManager, 'readTokens').mockReturnValue(null);

      // Perform health check
      const healthCheck = tokenMonitor.performHealthCheck();

      expect(healthCheck.isValid).toBe(false);
      expect(healthCheck.isExpired).toBe(true);
      expect(healthCheck.expiresIn).toBe(0);
    });
  });

  describe('Backup and Recovery Integration', () => {
    test('should restore tokens from backup when needed', async () => {
      const originalTokens = {
        accessToken: 'original_access_token',
        refreshToken: 'original_refresh_token',
        expiresIn: Date.now() + 3600000,
      };

      // Mock backup restoration
      jest.spyOn(tokenBackupService, 'getLatestBackup').mockResolvedValue(originalTokens);

      // Simulate token restoration
      const restoredTokens = await tokenBackupService.getLatestBackup();
      
      if (restoredTokens) {
        tokenManager.saveTokens(
          restoredTokens.accessToken,
          restoredTokens.refreshToken,
          restoredTokens.expiresIn
        );
      }

      // Verify tokens are restored
      const currentTokens = tokenManager.readTokens();
      expect(currentTokens).toEqual(originalTokens);
    });

    test('should validate backup integrity', async () => {
      const testTokens = {
        accessToken: 'integrity_test_token',
        refreshToken: 'integrity_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Mock backup validation
      const mockValidationResult = {
        total: 1,
        valid: 1,
        invalid: [],
      };

      jest.spyOn(tokenBackupService, 'validateAllBackups').mockResolvedValue(mockValidationResult);

      const validation = await tokenBackupService.validateAllBackups();
      
      expect(validation.total).toBe(1);
      expect(validation.valid).toBe(1);
      expect(validation.invalid).toHaveLength(0);
    });
  });

  describe('End-to-End Token Lifecycle', () => {
    test('should handle complete token lifecycle with encryption', async () => {
      // Step 1: Initial token storage
      const initialTokens = {
        accessToken: 'lifecycle_access_token',
        refreshToken: 'lifecycle_refresh_token',
        expiresIn: Date.now() + 3600000,
      };

      tokenManager.saveTokens(
        initialTokens.accessToken,
        initialTokens.refreshToken,
        initialTokens.expiresIn
      );

      // Step 2: Verify tokens are encrypted and stored
      const storedTokens = tokenManager.readTokens();
      expect(storedTokens).toEqual(initialTokens);

      // Step 3: Simulate token refresh
      const refreshedTokens = {
        accessToken: 'lifecycle_refreshed_token',
        refreshToken: initialTokens.refreshToken,
        expiresIn: Date.now() + 3600000,
      };

      tokenManager.saveTokens(
        refreshedTokens.accessToken,
        refreshedTokens.refreshToken,
        refreshedTokens.expiresIn
      );

      // Step 4: Verify refreshed tokens
      const finalTokens = tokenManager.readTokens();
      expect(finalTokens?.accessToken).toBe(refreshedTokens.accessToken);
      expect(finalTokens?.refreshToken).toBe(refreshedTokens.refreshToken);

      // Step 5: Verify monitoring works
      const healthCheck = tokenMonitor.performHealthCheck();
      expect(healthCheck.isValid).toBe(true);

      // Step 6: Verify storage status
      const status = tokenManager.getStorageStatus();
      expect(status).toHaveProperty('encryptionAvailable', true);
      expect(status).toHaveProperty('hasEncryptedTokens', true);
    });

    test('should maintain backward compatibility', () => {
      // Test that the new system works with the existing ClassZohoApi interface
      const ClassZohoApi = require('../ClassZohoApi').default;
      
      // Verify that getInstance method exists (singleton pattern)
      expect(typeof ClassZohoApi.getInstance).toBe('function');
      
      // Verify that key methods exist
      expect(typeof ClassZohoApi.getAccessTokenZoho).toBe('function');
      expect(typeof ClassZohoApi.renewAccessToken).toBe('function');
      expect(typeof ClassZohoApi.getCalendars).toBe('function');
    });
  });
});
