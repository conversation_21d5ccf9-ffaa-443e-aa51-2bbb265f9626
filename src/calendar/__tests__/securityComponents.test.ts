import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import * as fs from 'fs';
import * as path from 'path';
import { SecureTokenStorage, TokensData } from '../secureTokenStorage';
import { TokenManager } from '../tokenManager';
import { TokenBackupService } from '../tokenBackupService';
import { MigrationTools } from '../migrationTools';

// Mock environment variables
const mockEnvVars = {
  ZOHO_TOKEN_ENCRYPTION_KEY: 'dGVzdF9lbmNyeXB0aW9uX2tleV8zMl9ieXRlc19sb25n', // 32 bytes base64
  ZOHO_TOKEN_BACKUP_ENABLED: 'true',
  JWT_SECRET: 'test_jwt_secret_32_characters_long',
};

describe('SecureTokenStorage', () => {
  let secureStorage: SecureTokenStorage;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    Object.assign(process.env, mockEnvVars);
    secureStorage = new SecureTokenStorage();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Encryption/Decryption', () => {
    test('should encrypt and decrypt tokens correctly', () => {
      const testTokens: TokensData = {
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_456',
        expiresIn: Date.now() + 3600000,
      };

      const encrypted = secureStorage.encryptTokens(testTokens);
      expect(encrypted).toHaveProperty('encryptedData');
      expect(encrypted).toHaveProperty('iv');
      expect(encrypted).toHaveProperty('authTag');
      expect(encrypted).toHaveProperty('algorithm');
      expect(encrypted).toHaveProperty('timestamp');

      const decrypted = secureStorage.decryptTokens(encrypted);
      expect(decrypted).toEqual(testTokens);
    });

    test('should generate different IVs for each encryption', () => {
      const testTokens: TokensData = {
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      const encrypted1 = secureStorage.encryptTokens(testTokens);
      const encrypted2 = secureStorage.encryptTokens(testTokens);

      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      expect(encrypted1.encryptedData).not.toBe(encrypted2.encryptedData);
    });

    test('should fail decryption with tampered data', () => {
      const testTokens: TokensData = {
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      const encrypted = secureStorage.encryptTokens(testTokens);
      
      // Tamper with encrypted data
      encrypted.encryptedData = encrypted.encryptedData.slice(0, -5) + 'XXXXX';

      expect(() => {
        secureStorage.decryptTokens(encrypted);
      }).toThrow();
    });

    test('should fail with invalid encryption key', () => {
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = 'invalid_key';

      expect(() => {
        new SecureTokenStorage();
      }).toThrow();
    });

    test('should validate encrypted data structure', () => {
      const validData = {
        encryptedData: 'test',
        iv: 'test',
        authTag: 'test',
        algorithm: 'aes-256-gcm',
        timestamp: Date.now(),
      };

      const invalidData = {
        encryptedData: 'test',
        // missing required fields
      };

      expect(secureStorage.isValidEncryptedData(validData)).toBe(true);
      expect(secureStorage.isValidEncryptedData(invalidData)).toBe(false);
    });
  });

  describe('Key Generation', () => {
    test('should generate valid encryption key', () => {
      const key = SecureTokenStorage.generateEncryptionKey();
      
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(40); // Base64 encoded 32 bytes
      
      // Should be valid base64
      const decoded = Buffer.from(key, 'base64');
      expect(decoded.length).toBe(32);
    });
  });

  describe('Encryption Info', () => {
    test('should return encryption configuration', () => {
      const info = secureStorage.getEncryptionInfo();
      
      expect(info).toHaveProperty('algorithm');
      expect(info).toHaveProperty('keyLength');
      expect(info).toHaveProperty('keyInitialized');
    });
  });
});

describe('TokenManager', () => {
  let tokenManager: TokenManager;
  let originalEnv: NodeJS.ProcessEnv;
  let tempDir: string;

  beforeEach(() => {
    originalEnv = { ...process.env };
    Object.assign(process.env, mockEnvVars);
    
    // Create temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(__dirname, 'test-'));
    tokenManager = new TokenManager();
  });

  afterEach(() => {
    process.env = originalEnv;
    
    // Clean up temporary files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('Token Storage', () => {
    test('should save and read tokens with encryption', () => {
      const accessToken = 'test_access_token';
      const refreshToken = 'test_refresh_token';
      const expiresIn = Date.now() + 3600000;

      tokenManager.saveTokens(accessToken, refreshToken, expiresIn);
      
      const readTokens = tokenManager.readTokens();
      expect(readTokens).not.toBeNull();
      expect(readTokens?.accessToken).toBe(accessToken);
      expect(readTokens?.refreshToken).toBe(refreshToken);
      expect(readTokens?.expiresIn).toBe(expiresIn);
    });

    test('should handle missing tokens gracefully', () => {
      const tokens = tokenManager.readTokens();
      expect(tokens).toBeNull();
    });

    test('should provide storage status', () => {
      const status = tokenManager.getStorageStatus();
      
      expect(status).toHaveProperty('encryptionAvailable');
      expect(status).toHaveProperty('hasEncryptedTokens');
      expect(status).toHaveProperty('hasPlainTextTokens');
      expect(status).toHaveProperty('migrationStatus');
    });
  });

  describe('Migration', () => {
    test('should migrate from plain text to encrypted storage', () => {
      // Create mock plain text tokens file
      const plainTextTokens = {
        accessToken: 'plain_access_token',
        refreshToken: 'plain_refresh_token',
        expiresIn: Date.now() + 3600000,
      };

      // Mock file system operations
      const readFileSync = jest.spyOn(fs, 'readFileSync');
      const writeFileSync = jest.spyOn(fs, 'writeFileSync');
      const existsSync = jest.spyOn(fs, 'existsSync');

      existsSync.mockReturnValue(true);
      readFileSync.mockReturnValue(JSON.stringify(plainTextTokens));
      writeFileSync.mockImplementation(() => {});

      const tokens = tokenManager.readTokens();
      expect(tokens).not.toBeNull();

      readFileSync.mockRestore();
      writeFileSync.mockRestore();
      existsSync.mockRestore();
    });
  });
});

describe('TokenBackupService', () => {
  let backupService: TokenBackupService;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    Object.assign(process.env, mockEnvVars);
    backupService = new TokenBackupService();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Backup Operations', () => {
    test('should create backup when enabled', async () => {
      const testTokens: TokensData = {
        accessToken: 'backup_test_token',
        refreshToken: 'backup_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Mock Firebase operations
      const mockFirebaseDoc = {
        set: jest.fn().mockResolvedValue(undefined),
        update: jest.fn().mockResolvedValue(undefined),
      };

      const mockFirebaseCollection = {
        doc: jest.fn().mockReturnValue(mockFirebaseDoc),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue({ empty: true, docs: [] }),
      };

      // Mock the Firebase DB
      jest.doMock('../database/firebaseConfig', () => ({
        FirebaseDB: {
          collection: jest.fn().mockReturnValue(mockFirebaseCollection),
        },
      }));

      const backupId = await backupService.createBackup(testTokens);
      
      if (process.env.ZOHO_TOKEN_BACKUP_ENABLED === 'true') {
        expect(backupId).toBeTruthy();
        expect(mockFirebaseDoc.set).toHaveBeenCalled();
      }
    });

    test('should skip backup when disabled', async () => {
      process.env.ZOHO_TOKEN_BACKUP_ENABLED = 'false';
      
      const testTokens: TokensData = {
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      const backupId = await backupService.createBackup(testTokens);
      expect(backupId).toBeNull();
    });

    test('should return backup status', () => {
      const status = backupService.getBackupStatus();
      
      expect(status).toHaveProperty('enabled');
      expect(status).toHaveProperty('maxBackups');
      expect(status).toHaveProperty('collectionName');
    });
  });
});

describe('MigrationTools', () => {
  let migrationTools: MigrationTools;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    Object.assign(process.env, mockEnvVars);
    migrationTools = new MigrationTools();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Verification', () => {
    test('should verify encrypted storage', async () => {
      const result = await migrationTools.verifyEncryptedStorage();
      
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('encryptionWorking');
      expect(result).toHaveProperty('backupAccessible');
      expect(result).toHaveProperty('configurationValid');
      expect(result).toHaveProperty('issues');
      expect(result).toHaveProperty('recommendations');
    });

    test('should detect configuration issues', async () => {
      // Remove encryption key
      delete process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      
      const result = await migrationTools.verifyEncryptedStorage();
      expect(result.isValid).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
    });
  });

  describe('Migration', () => {
    test('should perform complete migration', async () => {
      // Mock file operations
      const existsSync = jest.spyOn(fs, 'existsSync');
      const readFileSync = jest.spyOn(fs, 'readFileSync');
      const writeFileSync = jest.spyOn(fs, 'writeFileSync');
      const mkdirSync = jest.spyOn(fs, 'mkdirSync');
      const copyFileSync = jest.spyOn(fs, 'copyFileSync');

      existsSync.mockReturnValue(true);
      readFileSync.mockReturnValue(JSON.stringify({
        accessToken: 'migration_test_token',
        refreshToken: 'migration_test_refresh',
        expiresIn: Date.now() + 3600000,
      }));
      writeFileSync.mockImplementation(() => {});
      mkdirSync.mockImplementation(() => '');
      copyFileSync.mockImplementation(() => {});

      const report = await migrationTools.performMigration({
        createBackup: true,
        removeOriginal: false,
        verifyAfterMigration: true,
      });

      expect(report).toHaveProperty('success');
      expect(report).toHaveProperty('steps');
      expect(report).toHaveProperty('filesProcessed');
      expect(report).toHaveProperty('errors');
      expect(report).toHaveProperty('recommendations');

      existsSync.mockRestore();
      readFileSync.mockRestore();
      writeFileSync.mockRestore();
      mkdirSync.mockRestore();
      copyFileSync.mockRestore();
    });
  });

  describe('Cleanup', () => {
    test('should clean up original files safely', () => {
      const unlinkSync = jest.spyOn(fs, 'unlinkSync');
      const existsSync = jest.spyOn(fs, 'existsSync');

      existsSync.mockReturnValue(true);
      unlinkSync.mockImplementation(() => {});

      const result = migrationTools.cleanupOriginalFiles();
      
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('filesRemoved');

      unlinkSync.mockRestore();
      existsSync.mockRestore();
    });
  });
});

describe('Integration Tests', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    Object.assign(process.env, mockEnvVars);
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  test('should handle complete token lifecycle', () => {
    const tokenManager = new TokenManager();
    const secureStorage = new SecureTokenStorage();

    // Save tokens
    const accessToken = 'lifecycle_access_token';
    const refreshToken = 'lifecycle_refresh_token';
    const expiresIn = Date.now() + 3600000;

    tokenManager.saveTokens(accessToken, refreshToken, expiresIn);

    // Read tokens
    const readTokens = tokenManager.readTokens();
    expect(readTokens).not.toBeNull();
    expect(readTokens?.accessToken).toBe(accessToken);

    // Verify encryption is working
    const info = secureStorage.getEncryptionInfo();
    expect(info).toHaveProperty('keyInitialized', true);

    // Check storage status
    const status = tokenManager.getStorageStatus();
    expect(status).toHaveProperty('encryptionAvailable', true);
  });

  test('should handle error scenarios gracefully', () => {
    // Test with invalid encryption key
    process.env.ZOHO_TOKEN_ENCRYPTION_KEY = 'invalid';

    expect(() => {
      new SecureTokenStorage();
    }).toThrow();

    // Test with missing encryption key
    delete process.env.ZOHO_TOKEN_ENCRYPTION_KEY;

    const tokenManager = new TokenManager();
    const status = tokenManager.getStorageStatus();
    expect(status).toHaveProperty('encryptionAvailable', false);
  });
});
