import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import * as crypto from 'crypto';
import { tokenManager } from '../tokenManager';
import { tokenBackupService } from '../tokenBackupService';
import { tokenMonitor } from '../tokenMonitor';
import { tokenRotationService } from '../tokenRotationService';
import { migrationTools } from '../migrationTools';
import { secureTokenStorage } from '../secureTokenStorage';

describe('End-to-End Security Validation', () => {
  let originalEnv: NodeJS.ProcessEnv;
  let testEncryptionKey: string;

  beforeEach(() => {
    originalEnv = { ...process.env };
    
    // Generate a valid test encryption key
    testEncryptionKey = crypto.randomBytes(32).toString('base64');
    
    process.env.ZOHO_TOKEN_ENCRYPTION_KEY = testEncryptionKey;
    process.env.ZOHO_TOKEN_BACKUP_ENABLED = 'true';
    process.env.JWT_SECRET = 'test_jwt_secret_32_characters_long';
    process.env.ZOHO_CLIENT_ID = 'test_client_id';
    process.env.ZOHO_CLIENT_SECRET = 'test_client_secret';
    process.env.ZOHO_REDIRECT_URL = 'http://localhost:3000/oauth2callback';
    process.env.ZOHO_CALENDAR_UID = 'test_calendar_uid';
    
    jest.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Complete Token Lifecycle Security', () => {
    test('should securely handle complete token lifecycle', async () => {
      // Phase 1: Initial token storage with encryption
      const initialTokens = {
        accessToken: 'secure_access_token_' + crypto.randomBytes(16).toString('hex'),
        refreshToken: 'secure_refresh_token_' + crypto.randomBytes(16).toString('hex'),
        expiresIn: Date.now() + 3600000,
      };

      // Store tokens securely
      tokenManager.saveTokens(
        initialTokens.accessToken,
        initialTokens.refreshToken,
        initialTokens.expiresIn
      );

      // Verify encryption is working
      const storageStatus = tokenManager.getStorageStatus();
      expect(storageStatus).toHaveProperty('encryptionAvailable', true);
      expect(storageStatus).toHaveProperty('hasEncryptedTokens', true);

      // Phase 2: Verify tokens can be read and decrypted
      const retrievedTokens = tokenManager.readTokens();
      expect(retrievedTokens).not.toBeNull();
      expect(retrievedTokens).toEqual(initialTokens);

      // Phase 3: Test token refresh with encryption
      const refreshedTokens = {
        accessToken: 'refreshed_access_token_' + crypto.randomBytes(16).toString('hex'),
        refreshToken: initialTokens.refreshToken,
        expiresIn: Date.now() + 3600000,
      };

      tokenManager.saveTokens(
        refreshedTokens.accessToken,
        refreshedTokens.refreshToken,
        refreshedTokens.expiresIn
      );

      const finalTokens = tokenManager.readTokens();
      expect(finalTokens?.accessToken).toBe(refreshedTokens.accessToken);
      expect(finalTokens?.refreshToken).toBe(refreshedTokens.refreshToken);

      // Phase 4: Verify backup creation and integrity
      const backupId = await tokenBackupService.createBackup(
        refreshedTokens,
        'manual',
        'manual_backup'
      );
      expect(backupId).toBeTruthy();

      // Phase 5: Test monitoring and health checks
      const healthCheck = tokenMonitor.performHealthCheck();
      expect(healthCheck.isValid).toBe(true);
      expect(healthCheck.isExpired).toBe(false);
    });

    test('should maintain security during token rotation', async () => {
      // Set up initial tokens
      const initialTokens = {
        accessToken: 'rotation_test_token',
        refreshToken: 'rotation_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      tokenManager.saveTokens(
        initialTokens.accessToken,
        initialTokens.refreshToken,
        initialTokens.expiresIn
      );

      // Perform encryption key rotation
      const rotationResult = await tokenRotationService.rotateEncryptionKey('manual', 'security_update');
      
      // Note: In a real test, this would require mocking the key rotation process
      // For now, we verify the rotation service is properly configured
      expect(rotationResult).toHaveProperty('success');
      expect(rotationResult).toHaveProperty('rotationId');

      // Verify rotation audit trail
      const auditLog = tokenRotationService.getAuditLog();
      expect(Array.isArray(auditLog)).toBe(true);

      // Verify rotation statistics
      const stats = tokenRotationService.getRotationStats();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('successRate');
    });
  });

  describe('Security Vulnerability Tests', () => {
    test('should prevent token tampering', () => {
      const testTokens = {
        accessToken: 'tamper_test_token',
        refreshToken: 'tamper_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Encrypt tokens
      const encrypted = secureTokenStorage.encryptTokens(testTokens);
      
      // Tamper with encrypted data
      const tamperedData = {
        ...encrypted,
        encryptedData: encrypted.encryptedData.slice(0, -10) + 'TAMPERED123',
      };

      // Attempt to decrypt tampered data should fail
      expect(() => {
        secureTokenStorage.decryptTokens(tamperedData);
      }).toThrow();
    });

    test('should prevent IV reuse attacks', () => {
      const testTokens = {
        accessToken: 'iv_test_token',
        refreshToken: 'iv_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Encrypt same data multiple times
      const encrypted1 = secureTokenStorage.encryptTokens(testTokens);
      const encrypted2 = secureTokenStorage.encryptTokens(testTokens);

      // IVs should be different
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      
      // Encrypted data should be different
      expect(encrypted1.encryptedData).not.toBe(encrypted2.encryptedData);
      
      // But both should decrypt to the same data
      const decrypted1 = secureTokenStorage.decryptTokens(encrypted1);
      const decrypted2 = secureTokenStorage.decryptTokens(encrypted2);
      expect(decrypted1).toEqual(decrypted2);
    });

    test('should validate encryption key strength', () => {
      // Test with weak key
      const weakKey = 'weak_key';
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = Buffer.from(weakKey).toString('base64');

      expect(() => {
        new (require('../secureTokenStorage').SecureTokenStorage)();
      }).toThrow();

      // Test with correct length key
      const strongKey = crypto.randomBytes(32).toString('base64');
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = strongKey;

      expect(() => {
        new (require('../secureTokenStorage').SecureTokenStorage)();
      }).not.toThrow();
    });

    test('should handle encryption key corruption gracefully', () => {
      // Set corrupted key
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = 'corrupted_base64_key!!!';

      expect(() => {
        new (require('../secureTokenStorage').SecureTokenStorage)();
      }).toThrow();

      // Verify fallback behavior in token manager
      const status = tokenManager.getStorageStatus();
      expect(status).toHaveProperty('encryptionAvailable', false);
    });
  });

  describe('Backup and Recovery Security', () => {
    test('should securely backup and restore tokens', async () => {
      const testTokens = {
        accessToken: 'backup_security_test_token',
        refreshToken: 'backup_security_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Create encrypted backup
      const backupId = await tokenBackupService.createBackup(
        testTokens,
        'manual',
        'manual_backup'
      );

      expect(backupId).toBeTruthy();

      // Restore from backup
      const restoredTokens = await tokenBackupService.restoreFromBackup(backupId!);
      expect(restoredTokens).toEqual(testTokens);

      // Verify backup integrity
      const validation = await tokenBackupService.validateAllBackups();
      expect(validation.valid).toBeGreaterThan(0);
      expect(validation.invalid).toHaveLength(0);
    });

    test('should detect corrupted backups', async () => {
      // This test would require mocking Firebase to return corrupted data
      // For now, we verify the validation mechanism exists
      const validation = await tokenBackupService.validateAllBackups();
      expect(validation).toHaveProperty('total');
      expect(validation).toHaveProperty('valid');
      expect(validation).toHaveProperty('invalid');
    });
  });

  describe('Monitoring and Alerting Security', () => {
    test('should detect and alert on token expiration', () => {
      // Set up tokens that expire soon
      const expiringTokens = {
        accessToken: 'expiring_token',
        refreshToken: 'expiring_refresh',
        expiresIn: Date.now() + 10 * 60 * 1000, // 10 minutes
      };

      tokenManager.saveTokens(
        expiringTokens.accessToken,
        expiringTokens.refreshToken,
        expiringTokens.expiresIn
      );

      // Perform health check
      const healthCheck = tokenMonitor.performHealthCheck();
      expect(healthCheck.isNearExpiration).toBe(true);
      expect(healthCheck.expiresIn).toBeLessThan(60 * 60 * 1000); // Less than 1 hour
    });

    test('should detect expired tokens', () => {
      // Set up expired tokens
      const expiredTokens = {
        accessToken: 'expired_token',
        refreshToken: 'expired_refresh',
        expiresIn: Date.now() - 3600000, // 1 hour ago
      };

      tokenManager.saveTokens(
        expiredTokens.accessToken,
        expiredTokens.refreshToken,
        expiredTokens.expiresIn
      );

      const healthCheck = tokenMonitor.performHealthCheck();
      expect(healthCheck.isExpired).toBe(true);
      expect(healthCheck.isValid).toBe(false);
    });

    test('should provide comprehensive monitoring status', () => {
      const monitoringStatus = tokenMonitor.getMonitoringStatus();
      
      expect(monitoringStatus).toHaveProperty('isRunning');
      expect(monitoringStatus).toHaveProperty('config');
      expect(monitoringStatus).toHaveProperty('lastHealthCheck');
      expect(monitoringStatus).toHaveProperty('nextCheckIn');
    });
  });

  describe('Migration Security Validation', () => {
    test('should securely migrate from plain text to encrypted storage', async () => {
      // Verify migration tools are properly configured
      const verification = await migrationTools.verifyEncryptedStorage();
      
      expect(verification).toHaveProperty('isValid');
      expect(verification).toHaveProperty('encryptionWorking');
      expect(verification).toHaveProperty('backupAccessible');
      expect(verification).toHaveProperty('configurationValid');

      // If encryption is working, verification should pass
      if (process.env.ZOHO_TOKEN_ENCRYPTION_KEY) {
        expect(verification.encryptionWorking).toBe(true);
      }
    });

    test('should validate migration prerequisites', async () => {
      // Test with missing encryption key
      delete process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      
      const verification = await migrationTools.verifyEncryptedStorage();
      expect(verification.isValid).toBe(false);
      expect(verification.issues.length).toBeGreaterThan(0);

      // Restore encryption key
      process.env.ZOHO_TOKEN_ENCRYPTION_KEY = testEncryptionKey;
      
      const verificationWithKey = await migrationTools.verifyEncryptedStorage();
      expect(verificationWithKey.encryptionWorking).toBe(true);
    });
  });

  describe('Security Audit and Compliance', () => {
    test('should provide comprehensive security status', () => {
      // Token Manager Security Status
      const tokenStatus = tokenManager.getStorageStatus();
      expect(tokenStatus).toHaveProperty('encryptionAvailable');
      expect(tokenStatus).toHaveProperty('encryptionInfo');
      expect(tokenStatus).toHaveProperty('backupStatus');

      // Backup Service Security Status
      const backupStatus = tokenBackupService.getBackupStatus();
      expect(backupStatus).toHaveProperty('enabled');
      expect(backupStatus).toHaveProperty('maxBackups');

      // Monitoring Security Status
      const monitoringStatus = tokenMonitor.getMonitoringStatus();
      expect(monitoringStatus).toHaveProperty('config');
      expect(monitoringStatus).toHaveProperty('isRunning');

      // Rotation Service Security Status
      const rotationStatus = tokenRotationService.getRotationStatus();
      expect(rotationStatus).toHaveProperty('config');
      expect(rotationStatus).toHaveProperty('stats');
    });

    test('should validate all security components are properly configured', () => {
      // Verify encryption configuration
      const encryptionInfo = secureTokenStorage.getEncryptionInfo();
      expect(encryptionInfo).toHaveProperty('algorithm', 'aes-256-gcm');
      expect(encryptionInfo).toHaveProperty('keyLength', 32);
      expect(encryptionInfo).toHaveProperty('keyInitialized', true);

      // Verify environment variables are set
      expect(process.env.ZOHO_TOKEN_ENCRYPTION_KEY).toBeTruthy();
      expect(process.env.JWT_SECRET).toBeTruthy();

      // Verify backup is enabled
      expect(process.env.ZOHO_TOKEN_BACKUP_ENABLED).toBe('true');
    });

    test('should generate security audit report', async () => {
      const auditReport = {
        timestamp: Date.now(),
        encryptionStatus: secureTokenStorage.getEncryptionInfo(),
        storageStatus: tokenManager.getStorageStatus(),
        backupStatus: tokenBackupService.getBackupStatus(),
        monitoringStatus: tokenMonitor.getMonitoringStatus(),
        rotationStatus: tokenRotationService.getRotationStatus(),
        migrationVerification: await migrationTools.verifyEncryptedStorage(),
      };

      // Verify audit report structure
      expect(auditReport).toHaveProperty('timestamp');
      expect(auditReport).toHaveProperty('encryptionStatus');
      expect(auditReport).toHaveProperty('storageStatus');
      expect(auditReport).toHaveProperty('backupStatus');
      expect(auditReport).toHaveProperty('monitoringStatus');
      expect(auditReport).toHaveProperty('rotationStatus');
      expect(auditReport).toHaveProperty('migrationVerification');

      // Verify critical security components are working
      expect(auditReport.encryptionStatus).toHaveProperty('keyInitialized', true);
      expect(auditReport.storageStatus).toHaveProperty('encryptionAvailable', true);
      expect(auditReport.migrationVerification).toHaveProperty('encryptionWorking', true);
    });
  });

  describe('Performance and Security Trade-offs', () => {
    test('should maintain acceptable performance with encryption', () => {
      const testTokens = {
        accessToken: 'performance_test_token',
        refreshToken: 'performance_test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      // Measure encryption performance
      const startTime = Date.now();
      
      for (let i = 0; i < 100; i++) {
        const encrypted = secureTokenStorage.encryptTokens(testTokens);
        const decrypted = secureTokenStorage.decryptTokens(encrypted);
        expect(decrypted).toEqual(testTokens);
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Should complete 100 encrypt/decrypt cycles in reasonable time (< 1 second)
      expect(totalTime).toBeLessThan(1000);
    });

    test('should handle concurrent token operations safely', async () => {
      const promises = [];
      
      // Simulate concurrent token operations
      for (let i = 0; i < 10; i++) {
        const promise = new Promise<void>((resolve) => {
          const tokens = {
            accessToken: `concurrent_token_${i}`,
            refreshToken: `concurrent_refresh_${i}`,
            expiresIn: Date.now() + 3600000,
          };
          
          tokenManager.saveTokens(tokens.accessToken, tokens.refreshToken, tokens.expiresIn);
          const retrieved = tokenManager.readTokens();
          
          // Last write should win
          expect(retrieved).toBeTruthy();
          resolve();
        });
        
        promises.push(promise);
      }
      
      await Promise.all(promises);
    });
  });
});
