import { FirebaseDB } from '../database/firebaseConfig';
import {
  secureTokenStorage,
  EncryptedTokenData,
  TokensData,
} from './secureTokenStorage';

/**
 * Interface for token backup metadata
 */
interface TokenBackupMetadata {
  backupId: string;
  createdAt: number;
  encryptionAlgorithm: string;
  backupType: 'automatic' | 'manual';
  source: 'token_refresh' | 'token_update' | 'manual_backup';
  validated: boolean;
}

/**
 * Interface for complete token backup document
 */
interface TokenBackupDocument {
  metadata: TokenBackupMetadata;
  encryptedTokens: EncryptedTokenData;
  checksum: string;
}

/**
 * Firebase Token Backup Service
 * Provides encrypted backup and restore functionality for Zoho tokens
 */
export class TokenBackupService {
  private readonly collectionName = 'zoho_token_backups';
  private readonly maxBackups = 10; // Keep last 10 backups

  constructor() {}

  /**
   * Check if backup is enabled via environment variable
   */
  private isBackupEnabled(): boolean {
    const enabled = process.env.ZOHO_TOKEN_BACKUP_ENABLED;
    return enabled === 'true' || enabled === '1';
  }

  /**
   * Generate a checksum for backup validation
   */
  private generateChecksum(data: EncryptedTokenData): string {
    // Simple hash function for backup validation
    const dataString = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Validate backup integrity using checksum
   */
  private validateBackup(backup: TokenBackupDocument): boolean {
    try {
      const calculatedChecksum = this.generateChecksum(backup.encryptedTokens);
      return calculatedChecksum === backup.checksum;
    } catch (error) {
      console.error('Error validating backup checksum:', error);
      return false;
    }
  }

  /**
   * Get Firebase collection reference
   */
  private getBackupCollection() {
    return FirebaseDB.collection(this.collectionName);
  }

  /**
   * Create encrypted backup of tokens
   */
  public async createBackup(
    tokenData: TokensData,
    backupType: 'automatic' | 'manual' = 'automatic',
    source: 'token_refresh' | 'token_update' | 'manual_backup' = 'token_update'
  ): Promise<string | null> {
    try {
      if (!this.isBackupEnabled()) {
        console.log('ℹ️  Token backup is disabled');
        return null;
      }

      console.log('🔄 Creating encrypted token backup...');

      // Encrypt the token data
      const encryptedTokens = secureTokenStorage.encryptTokens(tokenData);

      // Generate backup metadata
      const backupId = `backup_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;
      const metadata: TokenBackupMetadata = {
        backupId,
        createdAt: Date.now(),
        encryptionAlgorithm: encryptedTokens.algorithm,
        backupType,
        source,
        validated: false,
      };

      // Create backup document
      const backupDocument: TokenBackupDocument = {
        metadata,
        encryptedTokens,
        checksum: this.generateChecksum(encryptedTokens),
      };

      // Validate backup before saving
      if (!this.validateBackup(backupDocument)) {
        throw new Error('Backup validation failed before saving');
      }

      // Save to Firebase
      await this.getBackupCollection().doc(backupId).set(backupDocument);

      // Mark as validated
      await this.getBackupCollection().doc(backupId).update({
        'metadata.validated': true,
      });

      console.log(`✅ Token backup created successfully: ${backupId}`);

      // Clean up old backups
      await this.cleanupOldBackups();

      return backupId;
    } catch (error) {
      console.error('❌ Failed to create token backup:', error);
      return null;
    }
  }

  /**
   * Restore tokens from backup
   */
  public async restoreFromBackup(backupId: string): Promise<TokensData | null> {
    try {
      console.log(`🔄 Restoring tokens from backup: ${backupId}`);

      // Get backup document
      const backupDoc = await this.getBackupCollection().doc(backupId).get();

      if (!backupDoc.exists) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      const backupData = backupDoc.data() as TokenBackupDocument;

      // Validate backup integrity
      if (!this.validateBackup(backupData)) {
        throw new Error('Backup validation failed - data may be corrupted');
      }

      // Decrypt tokens
      const tokenData = secureTokenStorage.decryptTokens(
        backupData.encryptedTokens
      );

      console.log(`✅ Tokens restored successfully from backup: ${backupId}`);
      return tokenData;
    } catch (error) {
      console.error(`❌ Failed to restore from backup ${backupId}:`, error);
      return null;
    }
  }

  /**
   * List available backups
   */
  public async listBackups(): Promise<TokenBackupMetadata[]> {
    try {
      const snapshot = await this.getBackupCollection()
        .orderBy('metadata.createdAt', 'desc')
        .limit(this.maxBackups)
        .get();

      const backups: TokenBackupMetadata[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data() as TokenBackupDocument;
        backups.push(data.metadata);
      });

      return backups;
    } catch (error) {
      console.error('Error listing backups:', error);
      return [];
    }
  }

  /**
   * Get the most recent backup
   */
  public async getLatestBackup(): Promise<TokensData | null> {
    try {
      const snapshot = await this.getBackupCollection()
        .orderBy('metadata.createdAt', 'desc')
        .limit(1)
        .get();

      if (snapshot.empty) {
        console.log('ℹ️  No backups found');
        return null;
      }

      const latestDoc = snapshot.docs[0];
      const backupData = latestDoc.data() as TokenBackupDocument;

      // Validate and decrypt
      if (!this.validateBackup(backupData)) {
        throw new Error('Latest backup validation failed');
      }

      return secureTokenStorage.decryptTokens(backupData.encryptedTokens);
    } catch (error) {
      console.error('Error getting latest backup:', error);
      return null;
    }
  }

  /**
   * Clean up old backups (keep only the most recent ones)
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const snapshot = await this.getBackupCollection()
        .orderBy('metadata.createdAt', 'desc')
        .get();

      if (snapshot.size <= this.maxBackups) {
        return; // No cleanup needed
      }

      // Delete old backups beyond the limit
      const docsToDelete = snapshot.docs.slice(this.maxBackups);
      const batch = FirebaseDB.batch();

      docsToDelete.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log(`🧹 Cleaned up ${docsToDelete.length} old token backups`);
    } catch (error) {
      console.error('Error cleaning up old backups:', error);
    }
  }

  /**
   * Validate all backups and report status
   */
  public async validateAllBackups(): Promise<{
    total: number;
    valid: number;
    invalid: string[];
  }> {
    try {
      const snapshot = await this.getBackupCollection().get();
      const results = {
        total: snapshot.size,
        valid: 0,
        invalid: [] as string[],
      };

      for (const doc of snapshot.docs) {
        const backupData = doc.data() as TokenBackupDocument;

        if (this.validateBackup(backupData)) {
          results.valid++;
        } else {
          results.invalid.push(backupData.metadata.backupId);
        }
      }

      return results;
    } catch (error) {
      console.error('Error validating backups:', error);
      return { total: 0, valid: 0, invalid: [] };
    }
  }

  /**
   * Delete a specific backup
   */
  public async deleteBackup(backupId: string): Promise<boolean> {
    try {
      await this.getBackupCollection().doc(backupId).delete();
      console.log(`🗑️  Deleted backup: ${backupId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting backup ${backupId}:`, error);
      return false;
    }
  }

  /**
   * Get backup service status
   */
  public getBackupStatus(): object {
    return {
      enabled: this.isBackupEnabled(),
      maxBackups: this.maxBackups,
      collectionName: this.collectionName,
    };
  }
}

/**
 * Singleton instance for token backup service
 */
export const tokenBackupService = new TokenBackupService();
