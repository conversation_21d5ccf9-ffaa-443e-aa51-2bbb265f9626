import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const tokensFilePath = path.join(__dirname, 'tokens.json');

interface TokensData {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export function saveTokens(
  accessToken: string,
  refreshToken: string,
  expiresIn: number
): void {
  const data: TokensData = {
    accessToken,
    refreshToken,
    expiresIn,
  };
  fs.writeFileSync(tokensFilePath, JSON.stringify(data, null, 2));
}

export function readTokens(): TokensData | null {
  if (!fs.existsSync(tokensFilePath)) {
    return null;
  }
  try {
    const data = JSON.parse(fs.readFileSync(tokensFilePath, 'utf8'));
    return {
      accessToken: data.accessToken,
      refreshToken: data.refreshToken,
      expiresIn: data.expiresIn,
    };
  } catch (error) {
    console.error('Error reading tokens:', error);
    return null;
  }
}
