import * as crypto from 'crypto';

/**
 * Interface for encrypted token data structure
 */
export interface EncryptedTokenData {
  encryptedData: string;
  iv: string;
  authTag: string;
  algorithm: string;
  timestamp: number;
}

/**
 * Interface for plain token data (matches existing TokensData)
 */
export interface TokensData {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Configuration for encryption operations
 */
interface EncryptionConfig {
  algorithm: string;
  keyLength: number;
  ivLength: number;
  tagLength: number;
}

/**
 * Default encryption configuration using AES-256-GCM
 */
const DEFAULT_ENCRYPTION_CONFIG: EncryptionConfig = {
  algorithm: 'aes-256-gcm',
  keyLength: 32, // 256 bits
  ivLength: 16, // 128 bits
  tagLength: 16, // 128 bits
};

/**
 * Secure Token Storage Service
 * Provides AES-256-GCM encryption/decryption for Zoho OAuth tokens
 */
export class SecureTokenStorage {
  private readonly config: EncryptionConfig;
  private encryptionKey: Buffer | null = null;

  constructor(config: EncryptionConfig = DEFAULT_ENCRYPTION_CONFIG) {
    this.config = config;
  }

  /**
   * Initialize the encryption key from environment variable
   * @throws {Error} If encryption key is not properly configured
   */
  private initializeEncryptionKey(): void {
    if (this.encryptionKey) {
      return; // Already initialized
    }

    const keyEnvVar = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
    if (!keyEnvVar) {
      throw new Error(
        'ZOHO_TOKEN_ENCRYPTION_KEY environment variable is required for secure token storage'
      );
    }

    try {
      // Decode base64 key
      this.encryptionKey = Buffer.from(keyEnvVar, 'base64');

      // Validate key length
      if (this.encryptionKey.length !== this.config.keyLength) {
        throw new Error(
          `Invalid encryption key length. Expected ${this.config.keyLength} bytes, got ${this.encryptionKey.length} bytes`
        );
      }
    } catch (error) {
      throw new Error(
        `Failed to initialize encryption key: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Generate a random initialization vector (IV)
   * @returns {Buffer} Random IV buffer
   */
  private generateIV(): Buffer {
    return crypto.randomBytes(this.config.ivLength);
  }

  /**
   * Encrypt token data using AES-256-GCM
   * @param {TokensData} tokenData - Plain token data to encrypt
   * @returns {EncryptedTokenData} Encrypted token data with metadata
   * @throws {Error} If encryption fails
   */
  public encryptTokens(tokenData: TokensData): EncryptedTokenData {
    try {
      this.initializeEncryptionKey();

      if (!this.encryptionKey) {
        throw new Error('Encryption key not initialized');
      }

      // Generate random IV for this encryption operation
      const iv = this.generateIV();

      // Create cipher with IV (cast to CipherGCM for GCM-specific methods)
      const cipher = crypto.createCipheriv(
        this.config.algorithm,
        this.encryptionKey,
        iv
      ) as crypto.CipherGCM;
      cipher.setAAD(Buffer.from('zoho-tokens')); // Additional authenticated data

      // Encrypt the token data
      const plaintext = JSON.stringify(tokenData);
      let encrypted = cipher.update(plaintext, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Get authentication tag
      const authTag = cipher.getAuthTag();

      return {
        encryptedData: encrypted,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        algorithm: this.config.algorithm,
        timestamp: Date.now(),
      };
    } catch (error) {
      throw new Error(
        `Token encryption failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Decrypt token data using AES-256-GCM
   * @param {EncryptedTokenData} encryptedData - Encrypted token data to decrypt
   * @returns {TokensData} Decrypted token data
   * @throws {Error} If decryption fails or authentication fails
   */
  public decryptTokens(encryptedData: EncryptedTokenData): TokensData {
    try {
      this.initializeEncryptionKey();

      if (!this.encryptionKey) {
        throw new Error('Encryption key not initialized');
      }

      // Validate algorithm
      if (encryptedData.algorithm !== this.config.algorithm) {
        throw new Error(
          `Unsupported encryption algorithm: ${encryptedData.algorithm}`
        );
      }

      // Convert base64 strings back to buffers
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const authTag = Buffer.from(encryptedData.authTag, 'base64');

      // Create decipher (cast to DecipherGCM for GCM-specific methods)
      const decipher = crypto.createDecipheriv(
        this.config.algorithm,
        this.encryptionKey,
        iv
      ) as crypto.DecipherGCM;
      decipher.setAAD(Buffer.from('zoho-tokens')); // Same AAD used during encryption
      decipher.setAuthTag(authTag);

      // Decrypt the data
      let decrypted = decipher.update(
        encryptedData.encryptedData,
        'base64',
        'utf8'
      );
      decrypted += decipher.final('utf8');

      // Parse and validate the decrypted JSON
      const tokenData = JSON.parse(decrypted) as TokensData;

      // Validate required fields
      if (
        !tokenData.accessToken ||
        !tokenData.refreshToken ||
        !tokenData.expiresIn
      ) {
        throw new Error('Invalid token data structure after decryption');
      }

      return tokenData;
    } catch (error) {
      throw new Error(
        `Token decryption failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Validate encrypted token data structure
   * @param {any} data - Data to validate
   * @returns {boolean} True if data is valid EncryptedTokenData
   */
  public isValidEncryptedData(data: any): data is EncryptedTokenData {
    return (
      typeof data === 'object' &&
      data !== null &&
      typeof data.encryptedData === 'string' &&
      typeof data.iv === 'string' &&
      typeof data.authTag === 'string' &&
      typeof data.algorithm === 'string' &&
      typeof data.timestamp === 'number'
    );
  }

  /**
   * Generate a new encryption key for initial setup
   * @returns {string} Base64 encoded encryption key
   */
  public static generateEncryptionKey(): string {
    const key = crypto.randomBytes(DEFAULT_ENCRYPTION_CONFIG.keyLength);
    return key.toString('base64');
  }

  /**
   * Get encryption metadata for monitoring/debugging
   * @returns {object} Encryption configuration and status
   */
  public getEncryptionInfo(): object {
    return {
      algorithm: this.config.algorithm,
      keyLength: this.config.keyLength,
      ivLength: this.config.ivLength,
      tagLength: this.config.tagLength,
      keyInitialized: this.encryptionKey !== null,
    };
  }
}

/**
 * Singleton instance for secure token storage
 */
export const secureTokenStorage = new SecureTokenStorage();
