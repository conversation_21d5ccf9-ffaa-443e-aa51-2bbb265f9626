import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { tokenManager } from './tokenManager';
import { tokenBackupService } from './tokenBackupService';
import { secureTokenStorage } from './secureTokenStorage';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Interface for migration status report
 */
interface MigrationReport {
  timestamp: number;
  success: boolean;
  steps: {
    step: string;
    status: 'completed' | 'failed' | 'skipped';
    message: string;
    timestamp: number;
  }[];
  backupCreated?: string;
  filesProcessed: string[];
  errors: string[];
  recommendations: string[];
}

/**
 * Interface for verification results
 */
interface VerificationResult {
  isValid: boolean;
  encryptionWorking: boolean;
  backupAccessible: boolean;
  configurationValid: boolean;
  issues: string[];
  recommendations: string[];
}

/**
 * Migration and Cleanup Tools
 * Provides utilities for migrating existing installations to secure token storage
 */
export class MigrationTools {
  private readonly tokensFilePath = path.join(__dirname, 'tokens.json');
  private readonly encryptedTokensFilePath = path.join(__dirname, 'tokens.encrypted.json');
  private readonly migrationLogPath = path.join(__dirname, 'migration.log');

  /**
   * Perform complete migration from plain text to encrypted storage
   */
  public async performMigration(options: {
    createBackup?: boolean;
    removeOriginal?: boolean;
    verifyAfterMigration?: boolean;
  } = {}): Promise<MigrationReport> {
    const report: MigrationReport = {
      timestamp: Date.now(),
      success: false,
      steps: [],
      filesProcessed: [],
      errors: [],
      recommendations: [],
    };

    const {
      createBackup = true,
      removeOriginal = false,
      verifyAfterMigration = true,
    } = options;

    console.log('🚀 Starting Zoho token migration to encrypted storage...');

    try {
      // Step 1: Check prerequisites
      this.addStep(report, 'check_prerequisites', 'Checking migration prerequisites...');
      
      if (!this.checkPrerequisites()) {
        throw new Error('Prerequisites check failed');
      }

      this.completeStep(report, 'check_prerequisites', 'Prerequisites check passed');

      // Step 2: Backup existing configuration
      if (createBackup) {
        this.addStep(report, 'create_backup', 'Creating backup of current configuration...');
        
        const backupPath = await this.createConfigurationBackup();
        report.backupCreated = backupPath;
        
        this.completeStep(report, 'create_backup', `Backup created: ${backupPath}`);
      } else {
        this.skipStep(report, 'create_backup', 'Backup creation skipped by user');
      }

      // Step 3: Read existing tokens
      this.addStep(report, 'read_tokens', 'Reading existing plain text tokens...');
      
      const existingTokens = this.readPlainTextTokens();
      if (!existingTokens) {
        this.skipStep(report, 'read_tokens', 'No plain text tokens found to migrate');
      } else {
        report.filesProcessed.push(this.tokensFilePath);
        this.completeStep(report, 'read_tokens', 'Plain text tokens read successfully');
      }

      // Step 4: Migrate to encrypted storage
      if (existingTokens) {
        this.addStep(report, 'encrypt_tokens', 'Migrating tokens to encrypted storage...');
        
        tokenManager.saveTokens(
          existingTokens.accessToken,
          existingTokens.refreshToken,
          existingTokens.expiresIn
        );
        
        report.filesProcessed.push(this.encryptedTokensFilePath);
        this.completeStep(report, 'encrypt_tokens', 'Tokens encrypted and saved successfully');
      }

      // Step 5: Verify migration
      if (verifyAfterMigration) {
        this.addStep(report, 'verify_migration', 'Verifying migration results...');
        
        const verification = await this.verifyEncryptedStorage();
        if (!verification.isValid) {
          throw new Error(`Migration verification failed: ${verification.issues.join(', ')}`);
        }
        
        this.completeStep(report, 'verify_migration', 'Migration verification passed');
      }

      // Step 6: Create Firebase backup
      if (existingTokens) {
        this.addStep(report, 'firebase_backup', 'Creating Firebase backup...');
        
        const backupId = await tokenBackupService.createBackup(
          existingTokens,
          'manual',
          'manual_backup'
        );
        
        if (backupId) {
          this.completeStep(report, 'firebase_backup', `Firebase backup created: ${backupId}`);
        } else {
          this.failStep(report, 'firebase_backup', 'Firebase backup failed (non-critical)');
        }
      }

      // Step 7: Clean up original files
      if (removeOriginal && existingTokens) {
        this.addStep(report, 'cleanup_original', 'Removing original plain text files...');
        
        const cleanupResult = this.cleanupOriginalFiles();
        if (cleanupResult.success) {
          this.completeStep(report, 'cleanup_original', `Cleaned up: ${cleanupResult.filesRemoved.join(', ')}`);
        } else {
          this.failStep(report, 'cleanup_original', `Cleanup failed: ${cleanupResult.error}`);
        }
      } else {
        this.skipStep(report, 'cleanup_original', 'Original file cleanup skipped');
      }

      // Step 8: Generate recommendations
      this.generateRecommendations(report);

      report.success = true;
      console.log('✅ Migration completed successfully!');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      report.errors.push(errorMessage);
      console.error('❌ Migration failed:', errorMessage);
    }

    // Save migration report
    this.saveMigrationReport(report);
    return report;
  }

  /**
   * Check migration prerequisites
   */
  private checkPrerequisites(): boolean {
    const issues: string[] = [];

    // Check if encryption key is configured
    if (!process.env.ZOHO_TOKEN_ENCRYPTION_KEY) {
      issues.push('ZOHO_TOKEN_ENCRYPTION_KEY environment variable not set');
    }

    // Check if encryption key is valid
    try {
      const key = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      if (key) {
        const decoded = Buffer.from(key, 'base64');
        if (decoded.length !== 32) {
          issues.push('ZOHO_TOKEN_ENCRYPTION_KEY must be 32 bytes (256 bits)');
        }
      }
    } catch {
      issues.push('ZOHO_TOKEN_ENCRYPTION_KEY is not valid base64');
    }

    // Check write permissions
    try {
      const testFile = path.join(__dirname, 'test_write_permissions.tmp');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
    } catch {
      issues.push('No write permissions in calendar directory');
    }

    if (issues.length > 0) {
      console.error('❌ Prerequisites check failed:');
      issues.forEach(issue => console.error(`   - ${issue}`));
      return false;
    }

    return true;
  }

  /**
   * Read plain text tokens from legacy file
   */
  private readPlainTextTokens(): any {
    try {
      if (!fs.existsSync(this.tokensFilePath)) {
        return null;
      }

      const data = fs.readFileSync(this.tokensFilePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading plain text tokens:', error);
      return null;
    }
  }

  /**
   * Create backup of current configuration
   */
  private async createConfigurationBackup(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, 'backups');
    
    // Create backups directory if it doesn't exist
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const backupPath = path.join(backupDir, `migration-backup-${timestamp}`);
    fs.mkdirSync(backupPath, { recursive: true });

    // Backup existing files
    const filesToBackup = [
      this.tokensFilePath,
      this.encryptedTokensFilePath,
      this.migrationLogPath,
    ];

    for (const filePath of filesToBackup) {
      if (fs.existsSync(filePath)) {
        const fileName = path.basename(filePath);
        const backupFilePath = path.join(backupPath, fileName);
        fs.copyFileSync(filePath, backupFilePath);
      }
    }

    // Create backup manifest
    const manifest = {
      timestamp: Date.now(),
      backupPath,
      files: filesToBackup.filter(f => fs.existsSync(f)),
      environment: {
        hasEncryptionKey: !!process.env.ZOHO_TOKEN_ENCRYPTION_KEY,
        hasBackupEnabled: process.env.ZOHO_TOKEN_BACKUP_ENABLED === 'true',
      },
    };

    fs.writeFileSync(
      path.join(backupPath, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );

    return backupPath;
  }

  /**
   * Verify encrypted storage is working correctly
   */
  public async verifyEncryptedStorage(): Promise<VerificationResult> {
    const result: VerificationResult = {
      isValid: true,
      encryptionWorking: false,
      backupAccessible: false,
      configurationValid: false,
      issues: [],
      recommendations: [],
    };

    try {
      // Test encryption/decryption
      const testData = {
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        expiresIn: Date.now() + 3600000,
      };

      const encrypted = secureTokenStorage.encryptTokens(testData);
      const decrypted = secureTokenStorage.decryptTokens(encrypted);

      if (decrypted.accessToken === testData.accessToken) {
        result.encryptionWorking = true;
      } else {
        result.issues.push('Encryption/decryption test failed');
      }

      // Test backup service
      try {
        const backupStatus = tokenBackupService.getBackupStatus();
        result.backupAccessible = true;
      } catch (error) {
        result.issues.push('Backup service not accessible');
      }

      // Test configuration
      const storageStatus = tokenManager.getStorageStatus();
      if (storageStatus) {
        result.configurationValid = true;
      }

      // Check for existing encrypted tokens
      if (fs.existsSync(this.encryptedTokensFilePath)) {
        try {
          const tokens = tokenManager.readTokens();
          if (!tokens) {
            result.issues.push('Cannot read encrypted tokens');
          }
        } catch (error) {
          result.issues.push('Error reading encrypted tokens');
        }
      }

    } catch (error) {
      result.issues.push(`Verification error: ${error}`);
    }

    result.isValid = result.issues.length === 0;

    if (!result.isValid) {
      result.recommendations.push('Review encryption configuration');
      result.recommendations.push('Check environment variables');
      result.recommendations.push('Verify file permissions');
    }

    return result;
  }

  /**
   * Clean up original plain text files
   */
  public cleanupOriginalFiles(): { success: boolean; filesRemoved: string[]; error?: string } {
    const result = {
      success: true,
      filesRemoved: [] as string[],
      error: undefined as string | undefined,
    };

    try {
      const filesToRemove = [this.tokensFilePath];

      for (const filePath of filesToRemove) {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          result.filesRemoved.push(path.basename(filePath));
          console.log(`🗑️  Removed: ${path.basename(filePath)}`);
        }
      }

    } catch (error) {
      result.success = false;
      result.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return result;
  }

  /**
   * Helper methods for migration report
   */
  private addStep(report: MigrationReport, step: string, message: string): void {
    report.steps.push({
      step,
      status: 'completed', // Will be updated
      message,
      timestamp: Date.now(),
    });
  }

  private completeStep(report: MigrationReport, step: string, message: string): void {
    const stepEntry = report.steps.find(s => s.step === step);
    if (stepEntry) {
      stepEntry.status = 'completed';
      stepEntry.message = message;
    }
  }

  private failStep(report: MigrationReport, step: string, message: string): void {
    const stepEntry = report.steps.find(s => s.step === step);
    if (stepEntry) {
      stepEntry.status = 'failed';
      stepEntry.message = message;
    }
    report.errors.push(message);
  }

  private skipStep(report: MigrationReport, step: string, message: string): void {
    const stepEntry = report.steps.find(s => s.step === step);
    if (stepEntry) {
      stepEntry.status = 'skipped';
      stepEntry.message = message;
    }
  }

  private generateRecommendations(report: MigrationReport): void {
    report.recommendations.push('Update your .env file with the encryption key');
    report.recommendations.push('Test token functionality after migration');
    report.recommendations.push('Monitor token expiration and backup status');
    
    if (report.backupCreated) {
      report.recommendations.push(`Keep backup safe: ${report.backupCreated}`);
    }
  }

  private saveMigrationReport(report: MigrationReport): void {
    try {
      const reportPath = path.join(__dirname, `migration-report-${Date.now()}.json`);
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`📊 Migration report saved: ${reportPath}`);
    } catch (error) {
      console.error('Failed to save migration report:', error);
    }
  }
}

/**
 * Singleton instance for migration tools
 */
export const migrationTools = new MigrationTools();
