import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import {
  secureTokenStorage,
  TokensData,
  EncryptedTokenData,
} from './secureTokenStorage';
import { tokenBackupService } from './tokenBackupService';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File paths
const tokensFilePath = path.join(__dirname, 'tokens.json');
const encryptedTokensFilePath = path.join(__dirname, 'tokens.encrypted.json');
const migrationLogPath = path.join(__dirname, 'migration.log');

/**
 * Migration status tracking
 */
interface MigrationStatus {
  migrated: boolean;
  migratedAt: number;
  backupCreated: boolean;
  originalFileRemoved: boolean;
}

/**
 * Token Manager with backward compatibility and encryption support
 * Maintains the existing saveTokens/readTokens interface while adding security
 */
export class TokenManager {
  private migrationStatus: MigrationStatus | null = null;

  constructor() {
    this.loadMigrationStatus();
  }

  /**
   * Load migration status from log file
   */
  private loadMigrationStatus(): void {
    try {
      if (fs.existsSync(migrationLogPath)) {
        const logData = fs.readFileSync(migrationLogPath, 'utf8');
        this.migrationStatus = JSON.parse(logData);
      }
    } catch (error) {
      console.warn('Failed to load migration status:', error);
      this.migrationStatus = null;
    }
  }

  /**
   * Save migration status to log file
   */
  private saveMigrationStatus(status: MigrationStatus): void {
    try {
      fs.writeFileSync(migrationLogPath, JSON.stringify(status, null, 2));
      this.migrationStatus = status;
    } catch (error) {
      console.error('Failed to save migration status:', error);
    }
  }

  /**
   * Check if encryption is available and configured
   */
  private isEncryptionAvailable(): boolean {
    try {
      const encryptionKey = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
      return !!encryptionKey && encryptionKey.length > 0;
    } catch {
      return false;
    }
  }

  /**
   * Check if encrypted tokens file exists
   */
  private hasEncryptedTokens(): boolean {
    return fs.existsSync(encryptedTokensFilePath);
  }

  /**
   * Check if plain text tokens file exists
   */
  private hasPlainTextTokens(): boolean {
    return fs.existsSync(tokensFilePath);
  }

  /**
   * Read tokens from plain text file (legacy format)
   */
  private readPlainTextTokens(): TokensData | null {
    try {
      if (!this.hasPlainTextTokens()) {
        return null;
      }

      const data = JSON.parse(fs.readFileSync(tokensFilePath, 'utf8'));
      return {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        expiresIn: data.expiresIn,
      };
    } catch (error) {
      console.error('Error reading plain text tokens:', error);
      return null;
    }
  }

  /**
   * Read tokens from encrypted file
   */
  private readEncryptedTokens(): TokensData | null {
    try {
      if (!this.hasEncryptedTokens()) {
        return null;
      }

      const encryptedData = JSON.parse(
        fs.readFileSync(encryptedTokensFilePath, 'utf8')
      );

      if (!secureTokenStorage.isValidEncryptedData(encryptedData)) {
        throw new Error('Invalid encrypted token data format');
      }

      return secureTokenStorage.decryptTokens(encryptedData);
    } catch (error) {
      console.error('Error reading encrypted tokens:', error);
      return null;
    }
  }

  /**
   * Save tokens to encrypted file
   */
  private saveEncryptedTokens(tokenData: TokensData): void {
    try {
      const encryptedData = secureTokenStorage.encryptTokens(tokenData);
      fs.writeFileSync(
        encryptedTokensFilePath,
        JSON.stringify(encryptedData, null, 2)
      );
    } catch (error) {
      console.error('Error saving encrypted tokens:', error);
      throw error;
    }
  }

  /**
   * Save tokens to plain text file (fallback)
   */
  private savePlainTextTokens(tokenData: TokensData): void {
    try {
      fs.writeFileSync(tokensFilePath, JSON.stringify(tokenData, null, 2));
    } catch (error) {
      console.error('Error saving plain text tokens:', error);
      throw error;
    }
  }

  /**
   * Create backup of plain text tokens before migration
   */
  private createBackup(): boolean {
    try {
      if (!this.hasPlainTextTokens()) {
        return true; // No file to backup
      }

      const backupPath = path.join(
        __dirname,
        `tokens.backup.${Date.now()}.json`
      );
      fs.copyFileSync(tokensFilePath, backupPath);
      console.log(`✅ Token backup created: ${backupPath}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to create token backup:', error);
      return false;
    }
  }

  /**
   * Migrate tokens from plain text to encrypted storage
   */
  private migrateToEncrypted(): boolean {
    try {
      console.log('🔄 Starting token migration to encrypted storage...');

      // Check if already migrated
      if (this.migrationStatus?.migrated) {
        console.log('✅ Tokens already migrated to encrypted storage');
        return true;
      }

      // Check if encryption is available
      if (!this.isEncryptionAvailable()) {
        console.log('⚠️  Encryption not available, skipping migration');
        return false;
      }

      // Read existing plain text tokens
      const plainTextTokens = this.readPlainTextTokens();
      if (!plainTextTokens) {
        console.log('ℹ️  No plain text tokens found to migrate');
        return true;
      }

      // Create backup
      const backupCreated = this.createBackup();
      if (!backupCreated) {
        console.error('❌ Migration aborted: Failed to create backup');
        return false;
      }

      // Save as encrypted
      this.saveEncryptedTokens(plainTextTokens);
      console.log('✅ Tokens successfully encrypted and saved');

      // Verify encrypted tokens can be read
      const verifyTokens = this.readEncryptedTokens();
      if (
        !verifyTokens ||
        verifyTokens.accessToken !== plainTextTokens.accessToken ||
        verifyTokens.refreshToken !== plainTextTokens.refreshToken
      ) {
        throw new Error('Encrypted token verification failed');
      }

      // Update migration status
      const migrationStatus: MigrationStatus = {
        migrated: true,
        migratedAt: Date.now(),
        backupCreated: true,
        originalFileRemoved: false,
      };

      this.saveMigrationStatus(migrationStatus);
      console.log('✅ Token migration completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Token migration failed:', error);
      return false;
    }
  }

  /**
   * Read tokens with automatic migration and fallback
   * Maintains backward compatibility with existing code
   */
  public readTokens(): TokensData | null {
    try {
      // If encryption is available and we have encrypted tokens, use them
      if (this.isEncryptionAvailable() && this.hasEncryptedTokens()) {
        const encryptedTokens = this.readEncryptedTokens();
        if (encryptedTokens) {
          return encryptedTokens;
        }
      }

      // Try to migrate if we have plain text tokens and encryption is available
      if (this.isEncryptionAvailable() && this.hasPlainTextTokens()) {
        const migrationSuccess = this.migrateToEncrypted();
        if (migrationSuccess) {
          // Try reading encrypted tokens again
          const encryptedTokens = this.readEncryptedTokens();
          if (encryptedTokens) {
            return encryptedTokens;
          }
        }
      }

      // Fallback to plain text tokens
      const plainTextTokens = this.readPlainTextTokens();
      if (plainTextTokens) {
        console.log(
          '⚠️  Using plain text tokens (encryption not available or failed)'
        );
        return plainTextTokens;
      }

      return null;
    } catch (error) {
      console.error('Error reading tokens:', error);
      return null;
    }
  }

  /**
   * Save tokens with encryption when available
   * Maintains backward compatibility with existing code
   */
  public saveTokens(
    accessToken: string,
    refreshToken: string,
    expiresIn: number
  ): void {
    const tokenData: TokensData = {
      accessToken,
      refreshToken,
      expiresIn,
    };

    try {
      // Try to save as encrypted if encryption is available
      if (this.isEncryptionAvailable()) {
        this.saveEncryptedTokens(tokenData);
        console.log('✅ Tokens saved with encryption');

        // Create automatic backup
        this.createAutomaticBackup(tokenData);
        return;
      }

      // Fallback to plain text
      this.savePlainTextTokens(tokenData);
      console.log('⚠️  Tokens saved as plain text (encryption not available)');
    } catch (error) {
      console.error('Error saving tokens:', error);

      // If encrypted save failed, try plain text as emergency fallback
      if (this.isEncryptionAvailable()) {
        try {
          this.savePlainTextTokens(tokenData);
          console.log('⚠️  Tokens saved as plain text (encryption failed)');
        } catch (fallbackError) {
          console.error(
            '❌ Failed to save tokens even as plain text:',
            fallbackError
          );
          throw fallbackError;
        }
      } else {
        throw error;
      }
    }
  }

  /**
   * Create automatic backup of tokens
   */
  private createAutomaticBackup(tokenData: TokensData): void {
    // Run backup asynchronously to avoid blocking token save operation
    tokenBackupService
      .createBackup(tokenData, 'automatic', 'token_update')
      .then((backupId) => {
        if (backupId) {
          console.log(`📦 Automatic token backup created: ${backupId}`);
        }
      })
      .catch((error) => {
        console.error('⚠️  Automatic backup failed (non-critical):', error);
      });
  }

  /**
   * Get token storage status for monitoring
   */
  public getStorageStatus(): object {
    return {
      encryptionAvailable: this.isEncryptionAvailable(),
      hasEncryptedTokens: this.hasEncryptedTokens(),
      hasPlainTextTokens: this.hasPlainTextTokens(),
      migrationStatus: this.migrationStatus,
      encryptionInfo: this.isEncryptionAvailable()
        ? secureTokenStorage.getEncryptionInfo()
        : null,
      backupStatus: tokenBackupService.getBackupStatus(),
    };
  }
}

/**
 * Singleton instance for token management
 */
export const tokenManager = new TokenManager();

/**
 * Backward-compatible functions that maintain the existing API
 * These functions can be used as drop-in replacements for the old manageKeys.ts functions
 */
export function saveTokens(
  accessToken: string,
  refreshToken: string,
  expiresIn: number
): void {
  return tokenManager.saveTokens(accessToken, refreshToken, expiresIn);
}

export function readTokens(): TokensData | null {
  return tokenManager.readTokens();
}
