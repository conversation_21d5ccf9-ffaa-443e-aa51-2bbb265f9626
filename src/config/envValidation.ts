import { z } from 'zod';

/**
 * Schema de validación para variables de entorno Firebase
 */
const FirebaseEnvSchema = z.object({
  FIREBASE_PROJECT_ID: z
    .string()
    .min(1, 'FIREBASE_PROJECT_ID es requerido')
    .describe('ID del proyecto Firebase'),

  FIREBASE_CLIENT_EMAIL: z
    .string()
    .email('FIREBASE_CLIENT_EMAIL debe ser un email válido')
    .refine((val) => val.includes('@'), 'FIREBASE_CLIENT_EMAIL debe contener @')
    .describe('Email del service account de Firebase'),

  FIREBASE_PRIVATE_KEY: z
    .string()
    .min(1, 'FIREBASE_PRIVATE_KEY es requerido')
    .refine(
      (val) =>
        val.includes('-----BEGIN PRIVATE KEY-----') &&
        val.includes('-----END PRIVATE KEY-----'),
      'FIREBASE_PRIVATE_KEY debe ser una clave privada válida'
    )
    .describe('Clave privada del service account de Firebase'),
});

/**
 * Schema de validación para variables de entorno de la aplicación
 */
const AppEnvSchema = z.object({
  COLLECTION: z
    .string()
    .min(1, 'COLLECTION es requerido')
    .describe('Nombre de la colección principal en Firestore'),

  DOC_ID: z
    .string()
    .min(1, 'DOC_ID es requerido')
    .describe('ID del documento principal en Firestore'),

  PORT: z
    .string()
    .optional()
    .default('3008')
    .transform((val) => parseInt(val, 10))
    .refine(
      (val) => val > 0 && val < 65536,
      'PORT debe ser un número válido entre 1 y 65535'
    )
    .describe('Puerto del servidor'),

  JWT_SECRET: z
    .string()
    .min(
      32,
      'JWT_SECRET debe tener al menos 32 caracteres para mayor seguridad'
    )
    .optional()
    .default('TU_SECRETO_MUY_SEGURO')
    .describe('Clave secreta para firmar tokens JWT'),
});

/**
 * Schema de validación para variables de entorno de Zoho (opcional)
 */
const ZohoEnvSchema = z.object({
  ZOHO_CLIENT_ID: z
    .string()
    .optional()
    .describe('Client ID de Zoho Calendar API'),

  ZOHO_CLIENT_SECRET: z
    .string()
    .optional()
    .describe('Client Secret de Zoho Calendar API'),

  ZOHO_REDIRECT_URL: z
    .string()
    .url('ZOHO_REDIRECT_URL debe ser una URL válida')
    .optional()
    .describe('URL de redirección para OAuth de Zoho'),

  ZOHO_CALENDAR_UID: z
    .string()
    .optional()
    .describe('UID del calendario de Zoho'),

  ZOHO_TOKEN_ENCRYPTION_KEY: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      try {
        const decoded = Buffer.from(val, 'base64');
        return decoded.length === 32; // 256 bits for AES-256
      } catch {
        return false;
      }
    }, 'ZOHO_TOKEN_ENCRYPTION_KEY debe ser una clave base64 válida de 32 bytes (256 bits)')
    .describe('Clave de cifrado para tokens de Zoho (base64, 32 bytes)'),

  ZOHO_TOKEN_BACKUP_ENABLED: z
    .string()
    .optional()
    .default('true')
    .transform((val) => val.toLowerCase() === 'true')
    .describe('Habilitar respaldo de tokens en Firebase'),
});

/**
 * Schema completo de validación de variables de entorno
 */
const EnvSchema = FirebaseEnvSchema.merge(AppEnvSchema).merge(ZohoEnvSchema);

/**
 * Tipo TypeScript inferido del schema de validación
 */
export type EnvConfig = z.infer<typeof EnvSchema>;

/**
 * Valida las variables de entorno y retorna la configuración validada
 * @throws {Error} Si alguna variable de entorno es inválida
 */
export function validateEnv(): EnvConfig {
  try {
    const env = EnvSchema.parse(process.env);

    console.log('✅ Variables de entorno validadas correctamente');
    console.log(`📊 Proyecto Firebase: ${env.FIREBASE_PROJECT_ID}`);
    console.log(`🗂️  Colección: ${env.COLLECTION}/${env.DOC_ID}`);
    console.log(`🚀 Puerto: ${env.PORT}`);

    return env;
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Error en validación de variables de entorno:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });

      console.error('\n📋 Variables de entorno requeridas:');
      console.error('  - FIREBASE_PROJECT_ID: ID del proyecto Firebase');
      console.error('  - FIREBASE_CLIENT_EMAIL: Email del service account');
      console.error(
        '  - FIREBASE_PRIVATE_KEY: Clave privada del service account'
      );
      console.error('  - COLLECTION: Nombre de la colección en Firestore');
      console.error('  - DOC_ID: ID del documento principal');
      console.error('\n📋 Variables opcionales:');
      console.error('  - PORT: Puerto del servidor (default: 3008)');
      console.error('  - ZOHO_*: Variables para integración con Zoho Calendar');

      throw new Error('Variables de entorno inválidas. Revisa el archivo .env');
    }
    throw error;
  }
}

/**
 * Valida solo las variables de Firebase (útil para testing)
 */
export function validateFirebaseEnv() {
  try {
    return FirebaseEnvSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Error en validación de variables Firebase:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
      throw new Error('Variables de Firebase inválidas');
    }
    throw error;
  }
}
