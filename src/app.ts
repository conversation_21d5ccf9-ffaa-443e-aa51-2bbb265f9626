import './config/envConfig';
import {
  createBot,
  createProvider,
  createFlow,
  addKeyword,
  utils,
} from '@builderbot/bot';
import { MemoryDB as Database } from '@builderbot/bot';
import { <PERSON><PERSON><PERSON>rovider as Provider } from '@builderbot/provider-baileys';
import flows from './flows';
import firebaseServiceInstance from './database/firebaseService';
import { useCompanyStore } from './store/CompanyStore';
import { CompanyData } from './model/CompanyModel';
import { qrController } from './controllers/qr.controller';
import { sendMessageController } from './controllers/sendMessage.controller';
import { blackListController } from './controllers/blackList.controller';
import { cancelAppointmentController } from './controllers/cancelAppointment.controller';
import { deleteAppointmentController } from './controllers/deleteAppointment.controller';
import { acceptAppointmentController } from './controllers/acceptAppointment.controller';
import { availableHoursController } from './controllers/availableHoursController.controller';
import { validateAppointmentDateController } from './controllers/validateAppointmentDate.controller';
import { updateAppointmentController } from './controllers/updateAppointment.controller';
import { confirmAppointmentUpdateController } from './controllers/confirmAppointmentUpdate.controller';
import { validateAppointmentTokenController } from './controllers/validateAppointmentToken.controller';
import { getEventsController } from './controllers/getEvents.controller';
import { availableWeekHoursController } from './controllers/availableHoursByWeek.controller';
import { regenerateZohoTokensController } from './controllers/regenerateZohoTokens.controller';

const PORT = process.env.PORT ?? 3008;

// !! Usar whatsapp bussiness
// !! Agregar delays random en los mensajes flowDynamic, addAnswer
// !! Agrear un manejador de logs
// !! Como debugear una app de node js
// !! Agregar un Icono de reloj al hacer una peticion a firestore por si tarda en responder o "Escribiendo..."

const main = async () => {
  const updateCompanyData = useCompanyStore.getState().updateCompanyData;
  const adapterProvider = createProvider(Provider, {
    writeMyself: 'both',
  });
  const adapterDB = new Database();

  const { handleCtx, httpServer } = await createBot({
    flow: flows,
    provider: adapterProvider,
    database: adapterDB,
  });

  // adapterProvider.server.post(
  //   '/v1/register',
  //   handleCtx(async (bot, req, res) => {
  //     const { number, name } = req.body;
  //     await bot.dispatch('REGISTER_FLOW', { from: number, name });
  //     return res.end('trigger');
  //   })
  // );

  // adapterProvider.server.post(
  //   '/v1/samples',
  //   handleCtx(async (bot, req, res) => {
  //     const { number, name } = req.body;
  //     await bot.dispatch('SAMPLES', { from: number, name });
  //     return res.end('trigger');
  //   })
  // );

  // adapterProvider.server.post(
  //   '/v1/blacklist',
  //   handleCtx(async (bot, req, res) => {
  //     const { number, intent } = req.body;
  //     if (intent === 'remove') bot.blacklist.remove(number);
  //     if (intent === 'add') bot.blacklist.add(number);

  //     res.writeHead(200, { 'Content-Type': 'application/json' });
  //     return res.end(JSON.stringify({ status: 'ok', number, intent }));
  //   })
  // );

  // TODO: agregar mas seguridad y ver como moverlo a otro archivo
  adapterProvider.server.post('/v1/blacklist', handleCtx(blackListController));
  adapterProvider.server.post('/v1/message', handleCtx(sendMessageController));
  adapterProvider.server.post(
    '/v1/appointments/cancel',
    handleCtx(cancelAppointmentController)
  );
  adapterProvider.server.post(
    '/v1/appointments/delete',
    handleCtx(deleteAppointmentController)
  );
  adapterProvider.server.post(
    '/v1/appointments/accept',
    handleCtx(acceptAppointmentController)
  );
  adapterProvider.server.post(
    '/v1/appointments/update',
    handleCtx(updateAppointmentController)
  );
  adapterProvider.server.get(
    '/v1/appointments/available-hours',
    handleCtx(availableHoursController)
  );
  adapterProvider.server.get(
    '/v1/appointments/validate-date',
    handleCtx(validateAppointmentDateController)
  );
  adapterProvider.server.post(
    '/v1/appointments/confirm-appointment-update',
    handleCtx(confirmAppointmentUpdateController)
  );
  adapterProvider.server.get(
    '/v1/appointments/validate-appointment-token',
    handleCtx(validateAppointmentTokenController)
  );
  adapterProvider.server.get('/v1/events', handleCtx(getEventsController));
  adapterProvider.server.get(
    '/v1/available-week-hours',
    handleCtx(availableWeekHoursController)
  );
  adapterProvider.server.post(
    '/v1/zoho/regenerate-tokens',
    handleCtx(regenerateZohoTokensController)
  );

  adapterProvider.server.get('/v1/qr', handleCtx(qrController));

  firebaseServiceInstance.listenToDocument((error, data) => {
    if (error) {
      console.error('Error al escuchar cambios en el documento:', error);
      return;
    }
    console.log('Documento actualizado:', data);

    if (data) {
      const companyData = data as CompanyData;
      updateCompanyData(companyData);
    }
  });

  httpServer(+PORT);
};

main();
