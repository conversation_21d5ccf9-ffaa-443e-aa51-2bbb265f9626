import zohoInstance from '~/calendar/ClassZohoApi';
import firebaseServiceInstance from '~/database/firebaseService';
import { DeleteAppointmentParams } from '~/model/AppointmentModel';

/**
 * Elimina una cita utilizando la API de Zoho y actualiza Firebase.
 *
 * La función intenta eliminar el evento en Zoho. Si la respuesta indica que el evento fue eliminado
 * (estatus igual a 'deleted'), se procede a aplicar un borrado logico a la cita del registro de Firebase.
 *
 * @param {DeleteAppointmentParams} params - eventUid, etag, phone
 * @returns {Promise<boolean>} - Devuelve una promesa que se resuelve en `true` si la eliminación fue exitosa, o `false` en caso contrario.
 */
export async function cancelAppointment({
  eventUid,
  etag,
  phone,
  isRejected,
}: DeleteAppointmentParams): Promise<boolean> {
  try {
    console.log('🔄 Intentando eliminar evento de Zoho...', { eventUid, etag });

    const deleteEventResult = await zohoInstance.deleteEvent(eventUid, etag);
    console.log('📊 Resultado de eliminación de Zoho:', deleteEventResult);

    const eventStatus = deleteEventResult.events?.[0]?.estatus;
    console.log('Estado del evento en Zoho:', eventStatus);

    if (eventStatus === 'deleted') {
      console.log('✅ Evento eliminado en Zoho, actualizando Firebase...');
      const firestoreDeleteResult =
        await firebaseServiceInstance.cancelUserAppointmentByPhoneLogicalDeletion(
          phone,
          isRejected
        );
      console.log('📊 Resultado Firebase:', firestoreDeleteResult);
      return firestoreDeleteResult;
    } else {
      console.log('⚠️ Evento no fue marcado como eliminado en Zoho');
      // Aún así intentar actualizar Firebase si hay algún resultado
      if (deleteEventResult) {
        console.log('🔄 Intentando actualizar Firebase de todas formas...');
        const firestoreDeleteResult =
          await firebaseServiceInstance.cancelUserAppointmentByPhoneLogicalDeletion(
            phone,
            isRejected
          );
        return firestoreDeleteResult;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Error al eliminar la cita:', error);

    // Si el error es de Zoho pero necesitamos cancelar en Firebase de todas formas
    if (error && typeof error === 'object' && 'error' in error) {
      console.log('⚠️ Error de Zoho, pero intentando cancelar en Firebase...');
      try {
        const firestoreDeleteResult =
          await firebaseServiceInstance.cancelUserAppointmentByPhoneLogicalDeletion(
            phone,
            isRejected
          );
        console.log(
          '✅ Cancelación en Firebase exitosa a pesar del error de Zoho'
        );
        return firestoreDeleteResult;
      } catch (firebaseError) {
        console.error('❌ Error también en Firebase:', firebaseError);
      }
    }

    return false;
  }
}

export const deleteFirestoreAppointment = async (
  phone: string
): Promise<boolean> => {
  const res = await firebaseServiceInstance.deleteUserAppointmentByPhone(phone);

  return res;
};

export const acceptFirestoreAppointment = async (
  phone: string
): Promise<boolean> => {
  const res = await firebaseServiceInstance.acceptAppointment(phone);

  return res;
};
