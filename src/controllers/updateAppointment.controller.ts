import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { Appointment, AppointmentStates } from '~/model/AppointmentModel';
import zohoInstance from '~/calendar/ClassZohoApi';
import firebaseServiceInstance from '~/database/firebaseService';
import { addOneHourToISODate, generateNgrams } from '~/utils/functions';
import { Timestamp } from 'firebase-admin/firestore';
import { format } from 'date-fns';
import jwt from 'jsonwebtoken';
import { nanoid } from 'nanoid';

// TODO: que pasaria si un usuario modifica su cita en whatsapp y al mismo tiempo el dueño del negocio lo hace en la app??

interface UpdateAppointmentRequestBody {
  /**
   * Nueva fecha de inicio en formato ISO (por ejemplo, "2025-02-21T06:00:00.000Z")
   */
  date: string;
  message: string;
  oldAppointment: Appointment;
  phoneNumber: string;
}

interface UpdateAppointmentResponse {
  updatedAppointment: Appointment;
  message: string;
  hasUpdated: boolean;
}

/**
 * Endpoint para modificar la fecha y hora de una cita.
 *
 * El proceso es el siguiente:
 * 1. Se valida que se reciba la nueva fecha y la información de la cita a modificar.
 * 2. Se calcula la nueva fecha de fin (agregándole una hora a la fecha de inicio).
 * 3. Se actualiza el evento en Zoho Calendar.
 * 4. Se actualiza el documento en Firestore con los nuevos datos.
 *
 * Nota: La validacion de si el slot esta disponible o no, se hace en el front llamando a otro endpoint
 */
export const updateAppointmentController = async (
  bot: BotType,
  req: RequestType<UpdateAppointmentRequestBody>,
  res: ResponseType
) => {
  try {
    const {
      date: newStartDate,
      message,
      oldAppointment,
      phoneNumber,
    } = req.body;

    if (!newStartDate || !oldAppointment) {
      const errorResponse: ErrorResponse = {
        message: 'Parámetros inválidos. Se requiere "date" y "oldAppointment".',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Calcular la nueva fecha de fin (sumándole una hora a la fecha de inicio)
    const newEndDate = addOneHourToISODate(newStartDate);

    // Verificar si el nuevo slot está disponible en Zoho Calendar
    const isSlotAvailable = await zohoInstance.isSlotAvailable(
      newStartDate,
      newEndDate
    );
    if (!isSlotAvailable) {
      const errorResponse: ErrorResponse = {
        message: 'Este horario ya está ocupado. Por favor, selecciona otro.',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Actualizar el evento en Zoho Calendar utilizando los datos de la cita anterior
    let calendarCall = null;
    try {
      console.log('🔄 Actualizando evento en Zoho Calendar...');
      calendarCall = await zohoInstance.updateEvent({
        eventUID: oldAppointment.eventUid,
        startDate: newStartDate,
        endDate: newEndDate,
        user: oldAppointment.nombre,
        etag: oldAppointment.etag,
      });
      console.log('📊 Resultado de actualización Zoho:', calendarCall);
    } catch (zohoError) {
      console.error('❌ Error actualizando en Zoho:', zohoError);

      // Si hay error de tokens, intentar continuar con Firebase
      if (zohoError && typeof zohoError === 'object' && 'error' in zohoError) {
        console.log(
          '⚠️ Error de Zoho, pero continuando con actualización en Firebase...'
        );
        calendarCall = { status: false, error: zohoError };
      } else {
        const errorResponse: ErrorResponse = {
          message: 'Error al actualizar la cita en Zoho Calendar.',
          statusCode: ReponseStatusCode.ServerError,
        };
        res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
          'Content-Type': 'application/json',
        });
        res.end(JSON.stringify(errorResponse));
        return;
      }
    }

    if (!calendarCall?.status) {
      console.log('⚠️ Actualización en Zoho no exitosa, pero continuando...');
      // Continuar con la actualización en Firebase de todas formas
    }

    const eventDataResult = calendarCall.eventData;

    // Extraer la fecha y el rango de tiempo para actualizar en Firestore
    const splitedDate = newStartDate.split('T')[0]; // Ejemplo: "2025-02-21"
    const splitedStartTime = format(new Date(newStartDate), 'h:mm a');
    const splitedEndTime = format(new Date(newEndDate), 'h:mm a');

    // Antes de generar un nuevo token, invalidamos los tokens anteriores del usuario.
    await firebaseServiceInstance.invalidateOldAppointmentTokens(phoneNumber);

    // Generar token JWT para el link de confirmación
    const secret = process.env.JWT_SECRET || 'TU_SECRETO_MUY_SEGURO';
    const tokenPayload = {
      appointmentId: oldAppointment.user, // TODO: cambiar ID
      phoneNumber,
      newStartDate,
      newTime: splitedStartTime,
      eventUid: eventDataResult.uid,
      etag: eventDataResult.etag,
    };
    // // El token expira en 1 hora
    // const token = jwt.sign(tokenPayload, secret, { expiresIn: '1h' });

    // // Generar un código corto (por ejemplo, 10 caracteres)
    // const shortCode = nanoid(10);
    // // NOTA: al cambiar la duracion tambien debo cambiar esto
    // // Calcular el timestamp de expiración (en milisegundos)
    // const expiresAt = Date.now() + 3600 * 1000;

    // Token que expira en 5 minutos
    // const token = jwt.sign(tokenPayload, secret, { expiresIn: '5m' });
    // const shortCode = nanoid(10);
    // const expiresAt = Date.now() + 5 * 60 * 1000; // 5 minutos en milisegundos

    // // Token que expira en 1 hora
    const token = jwt.sign(tokenPayload, secret, { expiresIn: '1h' });
    const shortCode = nanoid(10);
    const expiresAt = Date.now() + 3600 * 1000; // 1 hora en milisegundos

    // // Token que expira en 1 día
    // const token = jwt.sign(tokenPayload, secret, { expiresIn: '1d' });
    // const shortCode = nanoid(10);
    // const expiresAt = Date.now() + 24 * 3600 * 1000;  // 24 horas en milisegundos

    // // Token que expira en 2 días
    // const token = jwt.sign(tokenPayload, secret, { expiresIn: '2d' });
    // const shortCode = nanoid(10);
    // const expiresAt = Date.now() + 2 * 24 * 3600 * 1000;  // 2 días en milisegundos

    // Almacenar la asociación (código corto => token y datos relevantes) en Firestore
    await firebaseServiceInstance.addAppointmentToken(shortCode, {
      token,
      phoneNumber,
      appointmentId: oldAppointment.user,
      newStartDate,
      newTime: splitedStartTime,
      createdAt: Date.now(),
      expiresAt,
      used: false,
    });

    // Formar la URL de confirmación usando el código corto
    const confirmationUrl = `http://localhost:3000/confirmar-actualizacion-cita?code=${shortCode}`;
    console.log({ confirmationUrl });

    // Construir el objeto de cita actualizado
    const newUpdatedAppointmentObject: Appointment = {
      ...oldAppointment,
      start: newStartDate,
      end: newEndDate,
      timestampDate: Timestamp.fromDate(new Date(newStartDate)),
      eventUid: eventDataResult.uid,
      viewEventURL: eventDataResult.viewEventURL,
      etag: eventDataResult.etag,
      status: AppointmentStates.Pendiente,
      cancelledAt: null,
      tokenCode: shortCode,
      tokenExpiresAt: expiresAt,
      confirmationUrl,
    };

    // Actualizar la cita en Firestore
    const updateResult = await firebaseServiceInstance.addOrUpdateAppointment(
      splitedDate,
      `${splitedStartTime}-${splitedEndTime}`,
      newUpdatedAppointmentObject
    );

    if (!updateResult) {
      const errorResponse: ErrorResponse = {
        message: 'Error al actualizar la cita en la base de datos.',
        statusCode: ReponseStatusCode.ServerError,
      };
      res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    const updatedMessage = `${message}\n\nConfirma o rechaza tu cita aquí: ${confirmationUrl}`;
    await bot.sendMessage(phoneNumber, updatedMessage, {});

    const response: SuccessResponse<UpdateAppointmentResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: {
        updatedAppointment: updateResult,
        message: 'La cita se ha actualizado exitosamente.',
        hasUpdated: true,
      },
    };

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } catch (error) {
    console.error('Error al actualizar la cita:', error);
    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al actualizar la cita.',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
