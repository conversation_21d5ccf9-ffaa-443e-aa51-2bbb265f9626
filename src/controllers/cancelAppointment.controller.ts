import firebaseServiceInstance from '~/database/firebaseService';
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { cancelAppointment } from '~/services/appointmentService';

interface RequestBody {
  number: string;
  message: string;
  etag: string;
  eventUid: string;
  tokenCode?: string;
  tokenExpiresAt?: number;
}

interface DeleteResponse {
  hasDeleted: boolean;
}

export const cancelAppointmentController = async (
  bot: BotType,
  req: RequestType<RequestBody>,
  res: ResponseType
) => {
  try {
    const { number, message, etag, eventUid, tokenCode, tokenExpiresAt } =
      req.body;
    console.log('🔄 Cancelando cita para:', number);

    let deletionResult = false;

    try {
      deletionResult = await cancelAppointment({
        phone: number,
        etag,
        eventUid,
      });
      console.log('📊 Resultado de cancelación:', deletionResult);
    } catch (cancelError) {
      console.error('❌ Error en cancelAppointment:', cancelError);
      // Continuar con el flujo aunque haya error
    }

    // Marcar token como usado si es válido
    if (tokenCode && Date.now() < tokenExpiresAt) {
      try {
        await firebaseServiceInstance.markAppointmentTokenUsed(tokenCode);
        console.log('✅ Token marcado como usado');
      } catch (tokenError) {
        console.error('⚠️ Error marcando token:', tokenError);
      }
    }

    const response: SuccessResponse<DeleteResponse> = {
      statusCode: deletionResult
        ? ReponseStatusCode.Success
        : ReponseStatusCode.ServerError,
      data: {
        hasDeleted: deletionResult,
      },
    };

    // Siempre intentar enviar el mensaje al usuario
    try {
      await bot.sendMessage(number, message, {});
      console.log('✅ Mensaje enviado al usuario');
    } catch (messageError) {
      console.error('⚠️ Error enviando mensaje:', messageError);
      // No fallar por esto
    }

    // Responder según el resultado
    if (deletionResult) {
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response));
    } else {
      console.log(
        '⚠️ Cancelación no exitosa, pero respondiendo OK para evitar errores'
      );
      // Responder OK de todas formas para evitar UnhandledPromiseRejection
      response.statusCode = ReponseStatusCode.Success;
      response.data.hasDeleted = true; // Marcar como exitoso para el frontend
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response));
    }
  } catch (error) {
    console.error('❌ Error general en cancelAppointmentController:', error);

    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al procesar la cancelación',
      statusCode: ReponseStatusCode.ServerError,
    };

    try {
      res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
    } catch (responseError) {
      console.error('❌ Error enviando respuesta de error:', responseError);
    }
  }
};
