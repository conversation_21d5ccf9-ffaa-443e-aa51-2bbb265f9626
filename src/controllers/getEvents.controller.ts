// controllers/eventsController.ts
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import zohoInstance from '~/calendar/ClassZohoApi';
import { EventDetails } from '~/model/ZohoCalendarModel';

// Interfaz para los parámetros de query
interface GetEventsQueryParams {
  start: string;
  end: string;
  view?: string;
}

// Interfaz para la respuesta de eventos
interface GetEventsResponseData {
  events: EventDetails[];
}

export const getEventsController = async (
  bot: BotType,
  req: RequestType<{}, GetEventsQueryParams>,
  res: ResponseType
) => {
  try {
    // Se castea req.query a la interfaz GetEventsQueryParams
    const { start, end, view } = req.query;
    if (!start || !end) {
      const errorResponse: ErrorResponse = {
        message: 'Par<PERSON>metros "start" y "end" son requeridos',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Convertir los parámetros a Date
    const startDate = new Date(start);
    const endDate = new Date(end);

    // Se llama al método que obtiene solo los eventos dentro del rango
    // Esto evita traer todos los eventos y luego filtrarlos
    const eventsData: EventDetails[] = await zohoInstance.getEventsByRange(
      startDate,
      endDate
    );

    const successResponse: SuccessResponse<GetEventsResponseData> = {
      statusCode: ReponseStatusCode.Success,
      data: { events: eventsData },
    };
    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(successResponse));
  } catch (error) {
    console.error('Error en getEventsController:', error);
    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
