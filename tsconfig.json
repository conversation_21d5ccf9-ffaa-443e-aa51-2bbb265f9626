{"compilerOptions": {"target": "ES2022", "module": "ES2022", "declaration": false, "declarationMap": false, "moduleResolution": "node", "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "outDir": "./dist", "baseUrl": "./", "rootDir": "./", "incremental": true, "skipLibCheck": true, "resolveJsonModule": true, "paths": {"~/*": ["./src/*"]}}, "include": ["**/*.js", "**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**", "**e2e**", "**mock**", "jest.config.js"]}