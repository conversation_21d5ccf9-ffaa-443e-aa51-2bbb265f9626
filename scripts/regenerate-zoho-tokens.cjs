#!/usr/bin/env node
/* eslint-disable */

/**
 * Script para regenerar tokens de Zoho cuando expiren
 */

require('dotenv').config();

console.log('🔄 Script de Regeneración de Tokens Zoho\n');

async function regenerateTokens() {
  try {
    const readline = require('readline');
    const axios = require('axios');
    const qs = require('qs');
    const fs = require('fs');

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    function question(prompt) {
      return new Promise((resolve) => {
        rl.question(prompt, resolve);
      });
    }

    console.log('📋 PASOS PARA REGENERAR TOKENS ZOHO:');
    console.log('');
    console.log('1. Ve a: https://api-console.zoho.com/');
    console.log('2. Selecciona tu aplicación');
    console.log('3. Ve a "Self Client" > "Generate Code"');
    console.log('4. Selecciona scopes: ZohoCalendar.calendar.ALL');
    console.log('5. Copia el código de autorización');
    console.log('');

    // Verificar configuración
    const clientId = process.env.ZOHO_CLIENT_ID;
    const clientSecret = process.env.ZOHO_CLIENT_SECRET;
    const redirectUrl = process.env.ZOHO_REDIRECT_URL;

    if (!clientId || !clientSecret || !redirectUrl) {
      console.error('❌ Faltan variables de entorno:');
      if (!clientId) console.error('   - ZOHO_CLIENT_ID');
      if (!clientSecret) console.error('   - ZOHO_CLIENT_SECRET');
      if (!redirectUrl) console.error('   - ZOHO_REDIRECT_URL');
      process.exit(1);
    }

    console.log('✅ Configuración encontrada:');
    console.log(`   Client ID: ${clientId.substring(0, 20)}...`);
    console.log(`   Redirect URL: ${redirectUrl}`);
    console.log('');

    // Solicitar código de autorización
    const authCode = await question(
      '📝 Ingresa el código de autorización de Zoho: '
    );

    if (!authCode || authCode.trim().length === 0) {
      console.error('❌ Código de autorización requerido');
      rl.close();
      process.exit(1);
    }

    console.log('\n🔄 Intercambiando código por tokens...');

    // Intercambiar código por tokens
    const data = qs.stringify({
      code: authCode.trim(),
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: redirectUrl,
      grant_type: 'authorization_code',
    });

    try {
      const response = await axios.post(
        'https://accounts.zoho.com/oauth/v2/token',
        data,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token, refresh_token, expires_in } = response.data;

      console.log('✅ Tokens obtenidos exitosamente!');
      console.log('');
      console.log('📊 Información de tokens:');
      console.log(`   Access Token: ${access_token.substring(0, 30)}...`);
      console.log(`   Refresh Token: ${refresh_token.substring(0, 30)}...`);
      console.log(
        `   Expira en: ${expires_in} segundos (${Math.round(
          expires_in / 3600
        )} horas)`
      );
      console.log('');

      // Preparar datos para guardar
      const tokenData = {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresIn: Date.now() + expires_in * 1000,
      };

      // Verificar si existe sistema de cifrado
      const hasEncryptionKey = !!process.env.ZOHO_TOKEN_ENCRYPTION_KEY;

      if (hasEncryptionKey) {
        console.log(
          '🔐 Sistema de cifrado detectado - guardando tokens cifrados...'
        );

        // Usar el sistema de cifrado
        const crypto = require('crypto');
        const encryptionKey = Buffer.from(
          process.env.ZOHO_TOKEN_ENCRYPTION_KEY,
          'base64'
        );
        const algorithm = 'aes-256-gcm';
        const iv = crypto.randomBytes(16);

        const cipher = crypto.createCipheriv(algorithm, encryptionKey, iv);
        cipher.setAAD(Buffer.from('zoho-tokens'));

        const plaintext = JSON.stringify(tokenData);
        let encrypted = cipher.update(plaintext, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        const authTag = cipher.getAuthTag();

        const encryptedData = {
          encryptedData: encrypted,
          iv: iv.toString('base64'),
          authTag: authTag.toString('base64'),
          algorithm: algorithm,
          timestamp: Date.now(),
        };

        // Guardar tokens cifrados
        fs.writeFileSync(
          'src/calendar/tokens.encrypted.json',
          JSON.stringify(encryptedData, null, 2)
        );
        console.log(
          '✅ Tokens cifrados guardados en: src/calendar/tokens.encrypted.json'
        );

        // Crear backup del archivo anterior si existe
        if (fs.existsSync('src/calendar/tokens.json')) {
          const backupPath = `src/calendar/tokens.backup.${Date.now()}.json`;
          fs.copyFileSync('src/calendar/tokens.json', backupPath);
          console.log(`📦 Backup creado: ${backupPath}`);
        }
      } else {
        console.log(
          '⚠️  Sistema de cifrado no configurado - guardando tokens en texto plano...'
        );

        // Guardar en texto plano
        fs.writeFileSync(
          'src/calendar/tokens.json',
          JSON.stringify(tokenData, null, 2)
        );
        console.log('✅ Tokens guardados en: src/calendar/tokens.json');
        console.log('');
        console.log(
          '🔐 RECOMENDACIÓN: Configura el sistema de cifrado para mayor seguridad:'
        );
        console.log(
          "   1. Genera clave: node -e \"console.log(require('crypto').randomBytes(32).toString('base64'))\""
        );
        console.log(
          '   2. Agrega al .env: ZOHO_TOKEN_ENCRYPTION_KEY=<clave-generada>'
        );
      }

      // Actualizar log de migración si existe
      if (fs.existsSync('src/calendar/migration.log')) {
        const migrationLog = {
          migrated: true,
          migratedAt: Date.now(),
          backupCreated: true,
          originalFileRemoved: false,
          tokensRegenerated: true,
          regeneratedAt: Date.now(),
        };

        fs.writeFileSync(
          'src/calendar/migration.log',
          JSON.stringify(migrationLog, null, 2)
        );
        console.log('📝 Log de migración actualizado');
      }

      console.log('');
      console.log('🎉 ¡Tokens regenerados exitosamente!');
      console.log('');
      console.log('📝 Próximos pasos:');
      console.log('   1. Reinicia la aplicación: npm start');
      console.log('   2. Verifica que funcione la integración con Zoho');
      console.log(
        '   3. Los tokens se renovarán automáticamente antes de expirar'
      );
      console.log('');
      console.log(
        '⏰ Recordatorio: Los tokens expiran en aproximadamente 1 hora'
      );
      console.log(
        '   El refresh token te permitirá renovarlos automáticamente'
      );
    } catch (error) {
      console.error(
        '❌ Error al obtener tokens:',
        error.response?.data || error.message
      );
      console.error('');
      console.error('🔍 Posibles causas:');
      console.error('   - Código de autorización expirado (genera uno nuevo)');
      console.error('   - Código de autorización ya usado');
      console.error('   - Configuración incorrecta de Client ID/Secret');
      console.error('   - URL de redirección no coincide');
      console.error('');
      console.error(
        '💡 Solución: Ve a Zoho API Console y genera un nuevo código'
      );
    }

    rl.close();
  } catch (error) {
    console.error('❌ Error en el script:', error.message);
    process.exit(1);
  }
}

// Ejecutar script
regenerateTokens().catch((error) => {
  console.error('❌ Error fatal:', error);
  process.exit(1);
});
