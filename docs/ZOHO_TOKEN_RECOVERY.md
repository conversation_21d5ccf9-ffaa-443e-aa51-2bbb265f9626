# Zoho Token Recovery Procedures

This document provides step-by-step recovery procedures for Zoho Calendar token issues in the botsito_be application.

## 🚨 Emergency Recovery Scenarios

### Scenario 1: Encrypted Tokens Cannot Be Decrypted

**Symptoms:**
- Error messages about decryption failures
- Application cannot read Zoho tokens
- "Token decryption failed" in logs

**Recovery Steps:**

1. **Check Environment Variables**
   ```bash
   # Verify encryption key is set
   echo $ZOHO_TOKEN_ENCRYPTION_KEY
   
   # Key should be 44 characters (32 bytes base64 encoded)
   echo $ZOHO_TOKEN_ENCRYPTION_KEY | wc -c
   ```

2. **Restore from Firebase Backup**
   ```typescript
   import { tokenBackupService } from './src/calendar/tokenBackupService';
   
   // List available backups
   const backups = await tokenBackupService.listBackups();
   console.log('Available backups:', backups);
   
   // Restore from latest backup
   const latestTokens = await tokenBackupService.getLatestBackup();
   if (latestTokens) {
     // Manually save tokens
     tokenManager.saveTokens(
       latestTokens.accessToken,
       latestTokens.refreshToken,
       latestTokens.expiresIn
     );
   }
   ```

3. **Restore from File Backup**
   ```bash
   # Find backup files
   ls -la src/calendar/backups/
   
   # Copy backup tokens.json back
   cp src/calendar/backups/migration-backup-*/tokens.json src/calendar/
   ```

### Scenario 2: All Tokens Lost or Corrupted

**Symptoms:**
- No token files found
- All backups corrupted
- Complete token loss

**Recovery Steps:**

1. **Re-authenticate with Zoho**
   - Go to Zoho API Console
   - Generate new authorization code
   - Use the authorization code to get new tokens

2. **Manual Token Entry**
   ```typescript
   // If you have tokens from another source
   import { tokenManager } from './src/calendar/tokenManager';
   
   tokenManager.saveTokens(
     'your_access_token',
     'your_refresh_token',
     Date.now() + 3600000 // 1 hour from now
   );
   ```

### Scenario 3: Encryption Key Lost

**Symptoms:**
- ZOHO_TOKEN_ENCRYPTION_KEY environment variable missing
- Cannot decrypt existing tokens
- New encryption key doesn't work with old tokens

**Recovery Steps:**

1. **Generate New Encryption Key**
   ```bash
   # Generate new 32-byte key
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
   ```

2. **Update Environment**
   ```bash
   # Add to .env file
   echo "ZOHO_TOKEN_ENCRYPTION_KEY=your_new_key_here" >> .env
   ```

3. **Restore Tokens and Re-encrypt**
   ```typescript
   // Restore from backup with old key, then save with new key
   const backupTokens = await tokenBackupService.getLatestBackup();
   if (backupTokens) {
     tokenManager.saveTokens(
       backupTokens.accessToken,
       backupTokens.refreshToken,
       backupTokens.expiresIn
     );
   }
   ```

## 🔧 Diagnostic Tools

### Check Token Storage Status

```typescript
import { tokenManager } from './src/calendar/tokenManager';

const status = tokenManager.getStorageStatus();
console.log('Storage Status:', JSON.stringify(status, null, 2));
```

### Verify Encryption System

```typescript
import { migrationTools } from './src/calendar/migrationTools';

const verification = await migrationTools.verifyEncryptedStorage();
console.log('Verification Result:', verification);
```

### List Available Backups

```typescript
import { tokenBackupService } from './src/calendar/tokenBackupService';

const backups = await tokenBackupService.listBackups();
console.log('Available Backups:', backups);

// Validate all backups
const validation = await tokenBackupService.validateAllBackups();
console.log('Backup Validation:', validation);
```

### Check Token Expiration

```typescript
import { tokenMonitor } from './src/calendar/tokenMonitor';

const healthCheck = tokenMonitor.performHealthCheck();
console.log('Token Health:', healthCheck);
```

## 🛠️ Manual Recovery Commands

### Force Token Migration

```typescript
import { migrationTools } from './src/calendar/migrationTools';

// Perform migration with all options
const report = await migrationTools.performMigration({
  createBackup: true,
  removeOriginal: false, // Keep original for safety
  verifyAfterMigration: true
});

console.log('Migration Report:', report);
```

### Manual Backup Creation

```typescript
import { tokenBackupService } from './src/calendar/tokenBackupService';
import { tokenManager } from './src/calendar/tokenManager';

const tokens = tokenManager.readTokens();
if (tokens) {
  const backupId = await tokenBackupService.createBackup(
    tokens,
    'manual',
    'manual_backup'
  );
  console.log('Backup created:', backupId);
}
```

### Reset to Plain Text (Emergency Only)

```bash
# Remove encrypted files
rm src/calendar/tokens.encrypted.json
rm src/calendar/migration.log

# Restore plain text backup
cp src/calendar/backups/migration-backup-*/tokens.json src/calendar/

# Temporarily disable encryption
unset ZOHO_TOKEN_ENCRYPTION_KEY
```

## 📋 Prevention Checklist

### Before Making Changes

- [ ] Create backup: `tokenBackupService.createBackup()`
- [ ] Verify current tokens work
- [ ] Document current encryption key
- [ ] Test recovery procedures

### Regular Maintenance

- [ ] Monitor token expiration: `tokenMonitor.performHealthCheck()`
- [ ] Validate backups: `tokenBackupService.validateAllBackups()`
- [ ] Check storage status: `tokenManager.getStorageStatus()`
- [ ] Review audit logs: `tokenRotationService.getAuditLog()`

### Environment Setup

- [ ] ZOHO_TOKEN_ENCRYPTION_KEY is set and valid
- [ ] ZOHO_TOKEN_BACKUP_ENABLED=true
- [ ] JWT_SECRET is configured
- [ ] Firebase credentials are valid

## 🔍 Troubleshooting Common Issues

### Issue: "Encryption key not initialized"

**Solution:**
```bash
# Check if key is set
echo $ZOHO_TOKEN_ENCRYPTION_KEY

# If not set, generate and add to .env
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Issue: "Firebase backup failed"

**Solution:**
```bash
# Check Firebase configuration
echo $FIREBASE_PROJECT_ID
echo $FIREBASE_CLIENT_EMAIL

# Test Firebase connection
npm run test:firebase
```

### Issue: "Token validation failed"

**Solution:**
```typescript
// Check token structure
const tokens = tokenManager.readTokens();
console.log('Token structure:', Object.keys(tokens || {}));

// Verify expiration
if (tokens) {
  console.log('Expires at:', new Date(tokens.expiresIn));
  console.log('Is expired:', Date.now() > tokens.expiresIn);
}
```

## 📞 Emergency Contacts

When all else fails:

1. **Check Application Logs**
   - Look for encryption/decryption errors
   - Check Firebase connection issues
   - Review token expiration warnings

2. **Restore from Known Good State**
   - Use file backups from `src/calendar/backups/`
   - Restore from Firebase backups
   - Re-authenticate with Zoho if necessary

3. **Document the Issue**
   - Save error logs
   - Note what was attempted
   - Record recovery steps taken

## 🔄 Recovery Validation

After any recovery procedure:

1. **Test Token Reading**
   ```typescript
   const tokens = tokenManager.readTokens();
   console.log('Tokens loaded:', !!tokens);
   ```

2. **Test Zoho API Call**
   ```typescript
   import zohoInstance from './src/calendar/ClassZohoApi';
   
   try {
     const calendars = await zohoInstance.getCalendars();
     console.log('API test successful');
   } catch (error) {
     console.error('API test failed:', error);
   }
   ```

3. **Create New Backup**
   ```typescript
   const backupId = await tokenBackupService.createBackup(tokens, 'manual', 'manual_backup');
   console.log('Recovery backup created:', backupId);
   ```

---

**Remember:** Always test recovery procedures in a development environment before applying to production!
